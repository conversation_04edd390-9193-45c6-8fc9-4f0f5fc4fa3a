import { test, expect } from '@playwright/test';

test.describe('Collection Management', () => {
	test.beforeEach(async ({ page }) => {
		// Navigate to the app
		await page.goto('/');
		
		// Mock authentication - assume user is logged in
		await page.evaluate(() => {
			localStorage.setItem('auth-token', 'mock-jwt-token');
		});
		
		// Navigate to collections page
		await page.goto('/collections');
	});

	test('should display collections page', async ({ page }) => {
		await expect(page).toHaveTitle(/Vocab/);
		await expect(page.locator('h1')).toContainText('Collections');
	});

	test('should create a new collection', async ({ page }) => {
		// Click create collection button
		await page.click('[data-testid="create-collection-btn"]');
		
		// Fill out the form
		await page.fill('[data-testid="collection-name-input"]', 'Test Collection');
		await page.selectOption('[data-testid="target-language-select"]', 'EN');
		await page.selectOption('[data-testid="source-language-select"]', 'VI');
		
		// Submit the form
		await page.click('[data-testid="create-collection-submit"]');
		
		// Wait for success message
		await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
		await expect(page.locator('[data-testid="success-message"]')).toContainText('Collection created successfully');
		
		// Verify collection appears in the list
		await expect(page.locator('[data-testid="collection-card"]')).toContainText('Test Collection');
	});

	test('should validate collection form', async ({ page }) => {
		// Click create collection button
		await page.click('[data-testid="create-collection-btn"]');
		
		// Try to submit without filling required fields
		await page.click('[data-testid="create-collection-submit"]');
		
		// Should show validation errors
		await expect(page.locator('[data-testid="name-error"]')).toBeVisible();
		await expect(page.locator('[data-testid="name-error"]')).toContainText('Name is required');
	});

	test('should edit collection name', async ({ page }) => {
		// Assume a collection exists
		await page.click('[data-testid="create-collection-btn"]');
		await page.fill('[data-testid="collection-name-input"]', 'Original Collection');
		await page.selectOption('[data-testid="target-language-select"]', 'EN');
		await page.selectOption('[data-testid="source-language-select"]', 'VI');
		await page.click('[data-testid="create-collection-submit"]');
		
		// Wait for collection to be created
		await expect(page.locator('[data-testid="collection-card"]')).toContainText('Original Collection');
		
		// Click edit button
		await page.click('[data-testid="edit-collection-btn"]');
		
		// Change the name
		await page.fill('[data-testid="collection-name-input"]', 'Updated Collection');
		await page.click('[data-testid="save-collection-btn"]');
		
		// Verify the name was updated
		await expect(page.locator('[data-testid="collection-card"]')).toContainText('Updated Collection');
	});

	test('should delete collection', async ({ page }) => {
		// Create a collection first
		await page.click('[data-testid="create-collection-btn"]');
		await page.fill('[data-testid="collection-name-input"]', 'Collection to Delete');
		await page.selectOption('[data-testid="target-language-select"]', 'EN');
		await page.selectOption('[data-testid="source-language-select"]', 'VI');
		await page.click('[data-testid="create-collection-submit"]');
		
		// Wait for collection to be created
		await expect(page.locator('[data-testid="collection-card"]')).toContainText('Collection to Delete');
		
		// Click delete button
		await page.click('[data-testid="delete-collection-btn"]');
		
		// Confirm deletion
		await page.click('[data-testid="confirm-delete-btn"]');
		
		// Verify collection is removed
		await expect(page.locator('[data-testid="collection-card"]')).not.toContainText('Collection to Delete');
	});

	test('should navigate to collection detail', async ({ page }) => {
		// Create a collection first
		await page.click('[data-testid="create-collection-btn"]');
		await page.fill('[data-testid="collection-name-input"]', 'Detail Collection');
		await page.selectOption('[data-testid="target-language-select"]', 'EN');
		await page.selectOption('[data-testid="source-language-select"]', 'VI');
		await page.click('[data-testid="create-collection-submit"]');
		
		// Wait for collection to be created
		await expect(page.locator('[data-testid="collection-card"]')).toContainText('Detail Collection');
		
		// Click on the collection card
		await page.click('[data-testid="collection-card"]');
		
		// Should navigate to collection detail page
		await expect(page).toHaveURL(/\/collections\/[^\/]+$/);
		await expect(page.locator('h1')).toContainText('Detail Collection');
	});

	test('should search collections', async ({ page }) => {
		// Create multiple collections
		const collections = ['English Vocabulary', 'Spanish Grammar', 'French Phrases'];
		
		for (const name of collections) {
			await page.click('[data-testid="create-collection-btn"]');
			await page.fill('[data-testid="collection-name-input"]', name);
			await page.selectOption('[data-testid="target-language-select"]', 'EN');
			await page.selectOption('[data-testid="source-language-select"]', 'VI');
			await page.click('[data-testid="create-collection-submit"]');
			await page.waitForTimeout(500); // Wait between creations
		}
		
		// Search for "English"
		await page.fill('[data-testid="search-input"]', 'English');
		
		// Should only show English collection
		await expect(page.locator('[data-testid="collection-card"]')).toHaveCount(1);
		await expect(page.locator('[data-testid="collection-card"]')).toContainText('English Vocabulary');
	});

	test('should handle empty state', async ({ page }) => {
		// If no collections exist, should show empty state
		await expect(page.locator('[data-testid="empty-state"]')).toBeVisible();
		await expect(page.locator('[data-testid="empty-state"]')).toContainText('No collections yet');
		await expect(page.locator('[data-testid="create-first-collection-btn"]')).toBeVisible();
	});

	test('should handle network errors gracefully', async ({ page }) => {
		// Intercept API calls and make them fail
		await page.route('/api/collections', route => {
			route.fulfill({
				status: 500,
				contentType: 'application/json',
				body: JSON.stringify({ error: 'Internal server error' })
			});
		});
		
		// Try to create a collection
		await page.click('[data-testid="create-collection-btn"]');
		await page.fill('[data-testid="collection-name-input"]', 'Test Collection');
		await page.selectOption('[data-testid="target-language-select"]', 'EN');
		await page.selectOption('[data-testid="source-language-select"]', 'VI');
		await page.click('[data-testid="create-collection-submit"]');
		
		// Should show error message
		await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
		await expect(page.locator('[data-testid="error-message"]')).toContainText('Failed to create collection');
	});

	test('should persist collections after page refresh', async ({ page }) => {
		// Create a collection
		await page.click('[data-testid="create-collection-btn"]');
		await page.fill('[data-testid="collection-name-input"]', 'Persistent Collection');
		await page.selectOption('[data-testid="target-language-select"]', 'EN');
		await page.selectOption('[data-testid="source-language-select"]', 'VI');
		await page.click('[data-testid="create-collection-submit"]');
		
		// Wait for collection to be created
		await expect(page.locator('[data-testid="collection-card"]')).toContainText('Persistent Collection');
		
		// Refresh the page
		await page.reload();
		
		// Collection should still be there
		await expect(page.locator('[data-testid="collection-card"]')).toContainText('Persistent Collection');
	});

	test('should show loading states', async ({ page }) => {
		// Intercept API calls to add delay
		await page.route('/api/collections', route => {
			setTimeout(() => {
				route.fulfill({
					status: 200,
					contentType: 'application/json',
					body: JSON.stringify([])
				});
			}, 1000);
		});
		
		// Should show loading spinner initially
		await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
		
		// Loading should disappear after API call completes
		await expect(page.locator('[data-testid="loading-spinner"]')).not.toBeVisible({ timeout: 2000 });
	});

	test('should handle concurrent collection creation', async ({ page, context }) => {
		// Open multiple tabs
		const page2 = await context.newPage();
		await page2.goto('/collections');
		
		// Create collections simultaneously
		const createCollection = async (page: any, name: string) => {
			await page.click('[data-testid="create-collection-btn"]');
			await page.fill('[data-testid="collection-name-input"]', name);
			await page.selectOption('[data-testid="target-language-select"]', 'EN');
			await page.selectOption('[data-testid="source-language-select"]', 'VI');
			await page.click('[data-testid="create-collection-submit"]');
		};
		
		await Promise.all([
			createCollection(page, 'Collection 1'),
			createCollection(page2, 'Collection 2')
		]);
		
		// Both collections should be created
		await expect(page.locator('[data-testid="collection-card"]')).toHaveCount(2);
		
		await page2.close();
	});
});
