import { test, expect } from '@playwright/test';

test.describe('Word Review Workflow', () => {
	test.beforeEach(async ({ page }) => {
		// Navigate to the app and mock authentication
		await page.goto('/');
		await page.evaluate(() => {
			localStorage.setItem('auth-token', 'mock-jwt-token');
		});
		
		// Create a test collection with words
		await page.goto('/collections');
		await page.click('[data-testid="create-collection-btn"]');
		await page.fill('[data-testid="collection-name-input"]', 'Review Test Collection');
		await page.selectOption('[data-testid="target-language-select"]', 'EN');
		await page.selectOption('[data-testid="source-language-select"]', 'VI');
		await page.click('[data-testid="create-collection-submit"]');
		
		// Wait for collection to be created and navigate to it
		await expect(page.locator('[data-testid="collection-card"]')).toContainText('Review Test Collection');
		await page.click('[data-testid="collection-card"]');
	});

	test('should start word review session', async ({ page }) => {
		// Mock API response for words to review
		await page.route('/api/collections/*/vocabulary/words-to-review*', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{
						id: 'word1',
						term: 'technology',
						language: 'EN',
						definitions: [{
							pos: ['noun'],
							ipa: '/tɛkˈnɑlədʒi/',
							explains: [{ EN: 'The application of scientific knowledge', VI: 'Ứng dụng kiến thức khoa học' }],
							examples: [{ EN: 'Technology has changed our lives', VI: 'Công nghệ đã thay đổi cuộc sống của chúng ta' }]
						}]
					},
					{
						id: 'word2',
						term: 'innovation',
						language: 'EN',
						definitions: [{
							pos: ['noun'],
							ipa: '/ˌɪnəˈveɪʃən/',
							explains: [{ EN: 'A new method or idea', VI: 'Một phương pháp hoặc ý tưởng mới' }],
							examples: [{ EN: 'Innovation drives progress', VI: 'Đổi mới thúc đẩy tiến bộ' }]
						}]
					}
				])
			});
		});
		
		// Click review button
		await page.click('[data-testid="start-review-btn"]');
		
		// Should navigate to review page
		await expect(page).toHaveURL(/\/collections\/[^\/]+\/vocabulary\/review$/);
		await expect(page.locator('h1')).toContainText('Word Review');
		
		// Should show first word
		await expect(page.locator('[data-testid="word-term"]')).toContainText('technology');
		await expect(page.locator('[data-testid="word-definition"]')).toContainText('The application of scientific knowledge');
	});

	test('should navigate through words during review', async ({ page }) => {
		// Mock words to review
		await page.route('/api/collections/*/vocabulary/words-to-review*', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'word1', term: 'technology', definitions: [{ explains: [{ EN: 'Tech definition' }] }] },
					{ id: 'word2', term: 'innovation', definitions: [{ explains: [{ EN: 'Innovation definition' }] }] }
				])
			});
		});
		
		// Mock mark as seen API
		await page.route('/api/last-seen-word', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify({ success: true })
			});
		});
		
		// Start review
		await page.click('[data-testid="start-review-btn"]');
		
		// Should show first word
		await expect(page.locator('[data-testid="word-term"]')).toContainText('technology');
		
		// Mark as seen and go to next
		await page.click('[data-testid="mark-seen-next-btn"]');
		
		// Should show second word
		await expect(page.locator('[data-testid="word-term"]')).toContainText('innovation');
		
		// Mark second word as seen
		await page.click('[data-testid="mark-seen-next-btn"]');
		
		// Should show completion message
		await expect(page.locator('[data-testid="review-complete"]')).toBeVisible();
		await expect(page.locator('[data-testid="review-complete"]')).toContainText('Review session completed');
	});

	test('should handle empty review session', async ({ page }) => {
		// Mock empty words response
		await page.route('/api/collections/*/vocabulary/words-to-review*', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([])
			});
		});
		
		// Click review button
		await page.click('[data-testid="start-review-btn"]');
		
		// Should show no words message
		await expect(page.locator('[data-testid="no-words-message"]')).toBeVisible();
		await expect(page.locator('[data-testid="no-words-message"]')).toContainText('No words to review');
		await expect(page.locator('[data-testid="generate-words-btn"]')).toBeVisible();
	});

	test('should restart review session', async ({ page }) => {
		// Mock words and API responses
		await page.route('/api/collections/*/vocabulary/words-to-review*', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'word1', term: 'test', definitions: [{ explains: [{ EN: 'Test definition' }] }] }
				])
			});
		});
		
		await page.route('/api/last-seen-word', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify({ success: true })
			});
		});
		
		// Start and complete review
		await page.click('[data-testid="start-review-btn"]');
		await page.click('[data-testid="mark-seen-next-btn"]');
		
		// Should show completion
		await expect(page.locator('[data-testid="review-complete"]')).toBeVisible();
		
		// Restart review
		await page.click('[data-testid="restart-review-btn"]');
		
		// Should start over
		await expect(page.locator('[data-testid="word-term"]')).toContainText('test');
	});

	test('should track review progress', async ({ page }) => {
		// Mock words
		await page.route('/api/collections/*/vocabulary/words-to-review*', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'word1', term: 'word1', definitions: [{ explains: [{ EN: 'Definition 1' }] }] },
					{ id: 'word2', term: 'word2', definitions: [{ explains: [{ EN: 'Definition 2' }] }] },
					{ id: 'word3', term: 'word3', definitions: [{ explains: [{ EN: 'Definition 3' }] }] }
				])
			});
		});
		
		await page.route('/api/last-seen-word', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify({ success: true })
			});
		});
		
		// Start review
		await page.click('[data-testid="start-review-btn"]');
		
		// Should show progress (1 of 3)
		await expect(page.locator('[data-testid="progress-indicator"]')).toContainText('1 of 3');
		
		// Go to next word
		await page.click('[data-testid="mark-seen-next-btn"]');
		
		// Should show progress (2 of 3)
		await expect(page.locator('[data-testid="progress-indicator"]')).toContainText('2 of 3');
		
		// Go to next word
		await page.click('[data-testid="mark-seen-next-btn"]');
		
		// Should show progress (3 of 3)
		await expect(page.locator('[data-testid="progress-indicator"]')).toContainText('3 of 3');
	});

	test('should handle API errors during review', async ({ page }) => {
		// Mock words
		await page.route('/api/collections/*/vocabulary/words-to-review*', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'word1', term: 'test', definitions: [{ explains: [{ EN: 'Test definition' }] }] }
				])
			});
		});
		
		// Mock failed mark as seen API
		await page.route('/api/last-seen-word', route => {
			route.fulfill({
				status: 500,
				contentType: 'application/json',
				body: JSON.stringify({ error: 'Failed to mark word as seen' })
			});
		});
		
		// Start review
		await page.click('[data-testid="start-review-btn"]');
		
		// Try to mark as seen
		await page.click('[data-testid="mark-seen-next-btn"]');
		
		// Should show error message
		await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
		await expect(page.locator('[data-testid="error-message"]')).toContainText('Failed to mark word as seen');
	});

	test('should navigate back to collection from review', async ({ page }) => {
		// Mock words
		await page.route('/api/collections/*/vocabulary/words-to-review*', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'word1', term: 'test', definitions: [{ explains: [{ EN: 'Test definition' }] }] }
				])
			});
		});
		
		// Start review
		await page.click('[data-testid="start-review-btn"]');
		
		// Click back button
		await page.click('[data-testid="back-to-collection-btn"]');
		
		// Should navigate back to collection
		await expect(page).toHaveURL(/\/collections\/[^\/]+$/);
		await expect(page.locator('h1')).toContainText('Review Test Collection');
	});

	test('should show word details during review', async ({ page }) => {
		// Mock word with detailed information
		await page.route('/api/collections/*/vocabulary/words-to-review*', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{
						id: 'word1',
						term: 'technology',
						language: 'EN',
						definitions: [{
							pos: ['noun'],
							ipa: '/tɛkˈnɑlədʒi/',
							explains: [{ EN: 'The application of scientific knowledge', VI: 'Ứng dụng kiến thức khoa học' }],
							examples: [{ EN: 'Technology has changed our lives', VI: 'Công nghệ đã thay đổi cuộc sống của chúng ta' }]
						}]
					}
				])
			});
		});
		
		// Start review
		await page.click('[data-testid="start-review-btn"]');
		
		// Should show word details
		await expect(page.locator('[data-testid="word-term"]')).toContainText('technology');
		await expect(page.locator('[data-testid="word-ipa"]')).toContainText('/tɛkˈnɑlədʒi/');
		await expect(page.locator('[data-testid="word-pos"]')).toContainText('noun');
		await expect(page.locator('[data-testid="word-definition"]')).toContainText('The application of scientific knowledge');
		await expect(page.locator('[data-testid="word-example"]')).toContainText('Technology has changed our lives');
	});

	test('should handle keyboard navigation during review', async ({ page }) => {
		// Mock words
		await page.route('/api/collections/*/vocabulary/words-to-review*', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'word1', term: 'word1', definitions: [{ explains: [{ EN: 'Definition 1' }] }] },
					{ id: 'word2', term: 'word2', definitions: [{ explains: [{ EN: 'Definition 2' }] }] }
				])
			});
		});
		
		await page.route('/api/last-seen-word', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify({ success: true })
			});
		});
		
		// Start review
		await page.click('[data-testid="start-review-btn"]');
		
		// Use keyboard to navigate (Space or Enter to mark as seen)
		await page.keyboard.press('Space');
		
		// Should move to next word
		await expect(page.locator('[data-testid="word-term"]')).toContainText('word2');
	});
});
