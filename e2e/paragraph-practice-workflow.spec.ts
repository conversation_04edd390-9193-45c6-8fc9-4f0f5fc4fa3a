import { test, expect } from '@playwright/test';

test.describe('Paragraph Practice Workflow', () => {
	test.beforeEach(async ({ page }) => {
		// Navigate to the app and mock authentication
		await page.goto('/');
		await page.evaluate(() => {
			localStorage.setItem('auth-token', 'mock-jwt-token');
		});
		
		// Create a test collection
		await page.goto('/collections');
		await page.click('[data-testid="create-collection-btn"]');
		await page.fill('[data-testid="collection-name-input"]', 'Paragraph Practice Collection');
		await page.selectOption('[data-testid="target-language-select"]', 'EN');
		await page.selectOption('[data-testid="source-language-select"]', 'VI');
		await page.click('[data-testid="create-collection-submit"]');
		
		// Navigate to collection detail
		await expect(page.locator('[data-testid="collection-card"]')).toContainText('Paragraph Practice Collection');
		await page.click('[data-testid="collection-card"]');
	});

	test('should start paragraph translation practice', async ({ page }) => {
		// Mock keywords API
		await page.route('/api/keywords', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'keyword1', content: 'technology' },
					{ id: 'keyword2', content: 'innovation' },
					{ id: 'keyword3', content: 'digital' }
				])
			});
		});

		// Mock paragraph generation API
		await page.route('/api/llm/generate-paragraph', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					'Technology has revolutionized the way we communicate and work in the modern world.',
					'Innovation drives progress and creates new opportunities for businesses and individuals.'
				])
			});
		});

		// Click paragraph practice button
		await page.click('[data-testid="paragraph-practice-btn"]');
		
		// Should navigate to paragraph practice page
		await expect(page).toHaveURL(/\/collections\/[^\/]+\/paragraph\/paragraph-practice$/);
		await expect(page.locator('h1')).toContainText('Paragraph Translation Practice');
		
		// Should show keyword selection
		await expect(page.locator('[data-testid="keyword-selection"]')).toBeVisible();
		await expect(page.locator('[data-testid="keyword-item"]')).toHaveCount(3);
	});

	test('should generate paragraphs for translation', async ({ page }) => {
		// Mock APIs
		await page.route('/api/keywords', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'keyword1', content: 'technology' },
					{ id: 'keyword2', content: 'innovation' }
				])
			});
		});

		await page.route('/api/llm/generate-paragraph', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					'Technology and innovation are driving forces in modern society.',
					'Digital transformation has changed how businesses operate globally.'
				])
			});
		});

		// Navigate to paragraph practice
		await page.click('[data-testid="paragraph-practice-btn"]');
		
		// Select keywords
		await page.click('[data-testid="keyword-item"]:nth-child(1)');
		await page.click('[data-testid="keyword-item"]:nth-child(2)');
		
		// Set difficulty
		await page.selectOption('[data-testid="difficulty-select"]', 'INTERMEDIATE');
		
		// Generate paragraphs
		await page.click('[data-testid="generate-paragraphs-btn"]');
		
		// Should show generated paragraphs
		await expect(page.locator('[data-testid="paragraph-item"]')).toHaveCount(2);
		await expect(page.locator('[data-testid="paragraph-text"]').first()).toContainText('Technology and innovation');
		
		// Should show translation input areas
		await expect(page.locator('[data-testid="translation-input"]')).toHaveCount(2);
	});

	test('should evaluate paragraph translations', async ({ page }) => {
		// Mock APIs
		await page.route('/api/keywords', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'keyword1', content: 'technology' }
				])
			});
		});

		await page.route('/api/llm/generate-paragraph', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					'Technology has transformed our daily lives significantly.'
				])
			});
		});

		await page.route('/api/llm/evaluate-translation', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify({
					score: 8,
					feedback: {
						source_language: 'Bản dịch tốt, có thể cải thiện một số từ ngữ.',
						target_language: 'Good translation, could improve some vocabulary.'
					},
					suggestions: ['Consider using more natural expressions']
				})
			});
		});

		// Navigate and generate paragraph
		await page.click('[data-testid="paragraph-practice-btn"]');
		await page.click('[data-testid="keyword-item"]:first-child');
		await page.click('[data-testid="generate-paragraphs-btn"]');
		
		// Enter translation
		await page.fill('[data-testid="translation-input"]', 'Công nghệ đã thay đổi cuộc sống hàng ngày của chúng ta một cách đáng kể.');
		
		// Submit for evaluation
		await page.click('[data-testid="evaluate-translation-btn"]');
		
		// Should show evaluation results
		await expect(page.locator('[data-testid="evaluation-score"]')).toContainText('8');
		await expect(page.locator('[data-testid="evaluation-feedback"]')).toContainText('Good translation');
		await expect(page.locator('[data-testid="evaluation-suggestions"]')).toContainText('Consider using more natural expressions');
	});

	test('should start Q&A practice', async ({ page }) => {
		// Mock APIs
		await page.route('/api/keywords', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'keyword1', content: 'science' },
					{ id: 'keyword2', content: 'research' }
				])
			});
		});

		await page.route('/api/llm/generate-paragraph-with-questions', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify({
					paragraph: 'Science and research are fundamental to human progress and understanding.',
					questions: [
						'What is fundamental to human progress?',
						'How does science contribute to understanding?',
						'Why is research important for society?'
					]
				})
			});
		});

		// Click Q&A practice button
		await page.click('[data-testid="qa-practice-btn"]');
		
		// Should navigate to Q&A practice page
		await expect(page).toHaveURL(/\/collections\/[^\/]+\/paragraph\/qa-practice$/);
		await expect(page.locator('h1')).toContainText('Q&A Practice');
		
		// Select keywords and generate
		await page.click('[data-testid="keyword-item"]:first-child');
		await page.click('[data-testid="keyword-item"]:nth-child(2)');
		await page.click('[data-testid="generate-qa-btn"]');
		
		// Should show paragraph and questions
		await expect(page.locator('[data-testid="practice-paragraph"]')).toContainText('Science and research');
		await expect(page.locator('[data-testid="question-item"]')).toHaveCount(3);
		await expect(page.locator('[data-testid="answer-input"]')).toHaveCount(3);
	});

	test('should evaluate Q&A answers', async ({ page }) => {
		// Mock APIs
		await page.route('/api/keywords', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'keyword1', content: 'science' }
				])
			});
		});

		await page.route('/api/llm/generate-paragraph-with-questions', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify({
					paragraph: 'Science is the systematic study of the natural world.',
					questions: ['What is science?']
				})
			});
		});

		await page.route('/api/llm/evaluate-answers', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{
						score: 9,
						feedback: {
							source_language: 'Câu trả lời chính xác và đầy đủ.',
							target_language: 'Accurate and complete answer.'
						}
					}
				])
			});
		});

		// Navigate and generate Q&A
		await page.click('[data-testid="qa-practice-btn"]');
		await page.click('[data-testid="keyword-item"]:first-child');
		await page.click('[data-testid="generate-qa-btn"]');
		
		// Answer the question
		await page.fill('[data-testid="answer-input"]', 'Science is the systematic study of the natural world through observation and experimentation.');
		
		// Submit answers
		await page.click('[data-testid="submit-answers-btn"]');
		
		// Should show evaluation results
		await expect(page.locator('[data-testid="answer-score"]')).toContainText('9');
		await expect(page.locator('[data-testid="answer-feedback"]')).toContainText('Accurate and complete answer');
	});

	test('should start grammar practice', async ({ page }) => {
		// Mock APIs
		await page.route('/api/keywords', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'keyword1', content: 'education' },
					{ id: 'keyword2', content: 'learning' }
				])
			});
		});

		await page.route('/api/llm/generate-grammar-practice', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{
						paragraphWithErrors: 'Education are very important for personal development and society progress.',
						correctedParagraph: 'Education is very important for personal development and social progress.',
						allErrors: [
							{
								errorText: 'are',
								correctedText: 'is',
								errorType: 'grammar',
								explanation: {
									source_language: 'Lỗi ngữ pháp: "Education" là danh từ số ít nên dùng "is"',
									target_language: 'Grammar error: "Education" is singular so use "is"'
								}
							},
							{
								errorText: 'society',
								correctedText: 'social',
								errorType: 'word_choice',
								explanation: {
									source_language: 'Lựa chọn từ: nên dùng "social progress" thay vì "society progress"',
									target_language: 'Word choice: should use "social progress" instead of "society progress"'
								}
							}
						]
					}
				])
			});
		});

		// Click grammar practice button
		await page.click('[data-testid="grammar-practice-btn"]');
		
		// Should navigate to grammar practice page
		await expect(page).toHaveURL(/\/collections\/[^\/]+\/paragraph\/grammar-practice$/);
		await expect(page.locator('h1')).toContainText('Grammar Practice');
		
		// Select keywords and generate
		await page.click('[data-testid="keyword-item"]:first-child');
		await page.selectOption('[data-testid="error-density-select"]', 'medium');
		await page.click('[data-testid="generate-grammar-btn"]');
		
		// Should show paragraph with errors
		await expect(page.locator('[data-testid="paragraph-with-errors"]')).toContainText('Education are very important');
		await expect(page.locator('[data-testid="error-highlight"]')).toHaveCount(2);
	});

	test('should identify and correct grammar errors', async ({ page }) => {
		// Mock APIs
		await page.route('/api/keywords', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'keyword1', content: 'technology' }
				])
			});
		});

		await page.route('/api/llm/generate-grammar-practice', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{
						paragraphWithErrors: 'Technology have changed our lifes dramatically.',
						correctedParagraph: 'Technology has changed our lives dramatically.',
						allErrors: [
							{
								errorText: 'have',
								correctedText: 'has',
								errorType: 'grammar',
								explanation: {
									source_language: 'Lỗi ngữ pháp: "Technology" là danh từ số ít',
									target_language: 'Grammar error: "Technology" is singular'
								}
							},
							{
								errorText: 'lifes',
								correctedText: 'lives',
								errorType: 'spelling',
								explanation: {
									source_language: 'Lỗi chính tả: số nhiều của "life" là "lives"',
									target_language: 'Spelling error: plural of "life" is "lives"'
								}
							}
						]
					}
				])
			});
		});

		// Navigate and generate grammar practice
		await page.click('[data-testid="grammar-practice-btn"]');
		await page.click('[data-testid="keyword-item"]:first-child');
		await page.click('[data-testid="generate-grammar-btn"]');
		
		// Click on first error
		await page.click('[data-testid="error-highlight"]:first-child');
		
		// Should show error explanation
		await expect(page.locator('[data-testid="error-explanation"]')).toBeVisible();
		await expect(page.locator('[data-testid="error-type"]')).toContainText('grammar');
		await expect(page.locator('[data-testid="error-description"]')).toContainText('Technology" is singular');
		
		// Should show correction suggestion
		await expect(page.locator('[data-testid="correction-suggestion"]')).toContainText('has');
	});

	test('should handle practice session completion', async ({ page }) => {
		// Mock APIs for paragraph practice
		await page.route('/api/keywords', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'keyword1', content: 'learning' }
				])
			});
		});

		await page.route('/api/llm/generate-paragraph', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					'Learning is a lifelong process that enriches our understanding.'
				])
			});
		});

		await page.route('/api/llm/evaluate-translation', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify({
					score: 7,
					feedback: {
						source_language: 'Bản dịch khá tốt.',
						target_language: 'Good translation.'
					}
				})
			});
		});

		// Mock stats tracking API
		await page.route('/api/collections/*/track-practice', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify({ success: true })
			});
		});

		// Complete a practice session
		await page.click('[data-testid="paragraph-practice-btn"]');
		await page.click('[data-testid="keyword-item"]:first-child');
		await page.click('[data-testid="generate-paragraphs-btn"]');
		await page.fill('[data-testid="translation-input"]', 'Học tập là một quá trình suốt đời làm phong phú hiểu biết của chúng ta.');
		await page.click('[data-testid="evaluate-translation-btn"]');
		
		// Complete session
		await page.click('[data-testid="complete-session-btn"]');
		
		// Should show completion summary
		await expect(page.locator('[data-testid="session-summary"]')).toBeVisible();
		await expect(page.locator('[data-testid="session-score"]')).toContainText('7');
		await expect(page.locator('[data-testid="practice-again-btn"]')).toBeVisible();
		await expect(page.locator('[data-testid="back-to-collection-btn"]')).toBeVisible();
	});

	test('should handle API errors gracefully', async ({ page }) => {
		// Mock keyword API to fail
		await page.route('/api/keywords', route => {
			route.fulfill({
				status: 500,
				contentType: 'application/json',
				body: JSON.stringify({ error: 'Failed to load keywords' })
			});
		});

		// Try to access paragraph practice
		await page.click('[data-testid="paragraph-practice-btn"]');
		
		// Should show error message
		await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
		await expect(page.locator('[data-testid="error-message"]')).toContainText('Failed to load keywords');
		await expect(page.locator('[data-testid="retry-btn"]')).toBeVisible();
	});

	test('should save practice progress', async ({ page }) => {
		// Mock APIs
		await page.route('/api/keywords', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					{ id: 'keyword1', content: 'progress' }
				])
			});
		});

		await page.route('/api/llm/generate-paragraph', route => {
			route.fulfill({
				status: 200,
				contentType: 'application/json',
				body: JSON.stringify([
					'Progress requires consistent effort and dedication.'
				])
			});
		});

		// Start practice
		await page.click('[data-testid="paragraph-practice-btn"]');
		await page.click('[data-testid="keyword-item"]:first-child');
		await page.click('[data-testid="generate-paragraphs-btn"]');
		
		// Enter partial translation
		await page.fill('[data-testid="translation-input"]', 'Tiến bộ đòi hỏi');
		
		// Navigate away and back
		await page.click('[data-testid="back-to-collection-btn"]');
		await page.click('[data-testid="paragraph-practice-btn"]');
		
		// Should restore progress
		await expect(page.locator('[data-testid="translation-input"]')).toHaveValue('Tiến bộ đòi hỏi');
	});
});
