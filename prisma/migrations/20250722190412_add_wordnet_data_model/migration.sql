-- CreateTable
CREATE TABLE "WordNetData" (
    "id" TEXT NOT NULL,
    "word_id" TEXT NOT NULL,
    "synsets" TEXT[],
    "lemma" TEXT,
    "hypernyms" TEXT[],
    "hyponyms" TEXT[],
    "holonyms" TEXT[],
    "meronyms" TEXT[],
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WordNetData_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "WordNetData_word_id_key" ON "WordNetData"("word_id");

-- AddForeignKey
ALTER TABLE "WordNetData" ADD CONSTRAINT "WordNetData_word_id_fkey" FOREIGN KEY ("word_id") REFERENCES "Word"("id") ON DELETE CASCADE ON UPDATE CASCADE;
