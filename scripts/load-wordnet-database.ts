#!/usr/bin/env tsx

/**
 * WordNet Database Loader Script
 * 
 * This script parses WordNet database files and loads them into the PostgreSQL database.
 * It processes the index and data files to extract words, definitions, and relationships.
 * 
 * Usage:
 *   yarn load-wordnet-database
 *   tsx scripts/load-wordnet-database.ts [options]
 * 
 * Features:
 * - Parses WordNet index and data files
 * - Extracts synsets, definitions, and relationships
 * - Loads words with complete WordNet data
 * - Supports batch processing for performance
 * - Provides progress tracking and statistics
 */

import fs from 'fs/promises';
import path from 'path';
import { PrismaClient, Language, PartsOfSpeech } from '@prisma/client';
import { WordNetDownloader } from './download-wordnet';

interface WordNetEntry {
	term: string;
	pos: PartsOfSpeech;
	synsets: string[];
	lemma: string;
	hypernyms: string[];
	hyponyms: string[];
	holonyms: string[];
	meronyms: string[];
	definitions: Array<{
		pos: PartsOfSpeech[];
		ipa: string;
		explains: Array<{ EN: string; VI: string }>;
		examples: Array<{ EN: string; VI: string }>;
	}>;
}

interface LoadOptions {
	pos?: string;
	maxWords?: number;
	batchSize?: number;
	dryRun?: boolean;
	force?: boolean;
	stats?: boolean;
}

interface SynsetData {
	offset: string;
	lexFileNum: string;
	ssType: string;
	words: Array<{ word: string; lexId: string }>;
	pointers: Array<{
		symbol: string;
		offset: string;
		pos: string;
		sourceTarget: string;
	}>;
	gloss: string;
}

class WordNetDatabaseLoader {
	private prisma: PrismaClient;
	private wordnetDir: string;
	
	// Part of speech mapping
	private readonly posMapping: Record<string, PartsOfSpeech> = {
		'n': PartsOfSpeech.NOUN,
		'v': PartsOfSpeech.VERB,
		'a': PartsOfSpeech.ADJECTIVE,
		's': PartsOfSpeech.ADJECTIVE, // Adjective satellite
		'r': PartsOfSpeech.ADVERB
	};

	// Pointer symbol meanings
	private readonly pointerMeanings: Record<string, string> = {
		'!': 'antonym',
		'@': 'hypernym',
		'~': 'hyponym',
		'#m': 'member_holonym',
		'#s': 'substance_holonym',
		'#p': 'part_holonym',
		'%m': 'member_meronym',
		'%s': 'substance_meronym',
		'%p': 'part_meronym',
		'=': 'attribute',
		'+': 'derivationally_related',
		';c': 'domain_of_synset_topic',
		'-c': 'member_of_this_domain_topic',
		';r': 'domain_of_synset_region',
		'-r': 'member_of_this_domain_region',
		';u': 'domain_of_synset_usage',
		'-u': 'member_of_this_domain_usage'
	};

	constructor() {
		this.prisma = new PrismaClient();
		this.wordnetDir = path.join(process.cwd(), 'data', 'wordnet', 'extracted');
	}

	async load(options: LoadOptions = {}): Promise<void> {
		console.log('🚀 Starting WordNet database loading...');
		
		try {
			// Ensure WordNet files are available
			await this.ensureWordNetFiles();
			
			// Show statistics if requested
			if (options.stats) {
				await this.showStatistics();
				return;
			}
			
			// Load data for specified parts of speech
			const posToLoad = options.pos ? [options.pos] : ['noun', 'verb', 'adj', 'adv'];
			
			for (const pos of posToLoad) {
				console.log(`\n📚 Processing ${pos}...`);
				await this.loadPartOfSpeech(pos, options);
			}
			
			console.log('\n✅ WordNet database loading completed!');
			
		} catch (error) {
			console.error('❌ Error during WordNet loading:', error);
			throw error;
		} finally {
			await this.prisma.$disconnect();
		}
	}

	private async ensureWordNetFiles(): Promise<void> {
		try {
			await fs.access(this.wordnetDir);
		} catch {
			console.log('📥 WordNet files not found. Downloading...');
			const downloader = new WordNetDownloader();
			await downloader.download();
		}
	}

	private async showStatistics(): Promise<void> {
		console.log('📊 WordNet Database Statistics:\n');
		
		const posFiles = ['noun', 'verb', 'adj', 'adv'];
		
		for (const pos of posFiles) {
			const indexFile = path.join(this.wordnetDir, `index.${pos}`);
			try {
				const content = await fs.readFile(indexFile, 'utf-8');
				const lines = content.split('\n').filter(line => 
					line.trim() && !line.startsWith('  ')
				);
				console.log(`${pos}: ${lines.length.toLocaleString()} words`);
			} catch (error) {
				console.log(`${pos}: Error reading file`);
			}
		}
		
		// Show current database stats
		const dbStats = await this.getDatabaseStats();
		console.log('\n📊 Current Database Statistics:');
		console.log(`Total words: ${dbStats.totalWords.toLocaleString()}`);
		console.log(`Words with WordNet data: ${dbStats.wordsWithWordNet.toLocaleString()}`);
	}

	private async getDatabaseStats() {
		const totalWords = await this.prisma.word.count();
		const wordsWithWordNet = await this.prisma.word.count({
			where: { wordnet_data: { isNot: null } }
		});
		
		return { totalWords, wordsWithWordNet };
	}

	private async loadPartOfSpeech(pos: string, options: LoadOptions): Promise<void> {
		const indexFile = path.join(this.wordnetDir, `index.${pos}`);
		const dataFile = path.join(this.wordnetDir, `data.${pos}`);
		
		// Read and parse index file
		const indexEntries = await this.parseIndexFile(indexFile);
		console.log(`📖 Found ${indexEntries.length} entries in index.${pos}`);
		
		// Read and parse data file
		const synsetData = await this.parseDataFile(dataFile);
		console.log(`📖 Found ${Object.keys(synsetData).length} synsets in data.${pos}`);
		
		// Process entries in batches
		const batchSize = options.batchSize || 100;
		const maxWords = options.maxWords || indexEntries.length;
		const entriesToProcess = indexEntries.slice(0, maxWords);
		
		console.log(`🔄 Processing ${entriesToProcess.length} entries in batches of ${batchSize}...`);
		
		for (let i = 0; i < entriesToProcess.length; i += batchSize) {
			const batch = entriesToProcess.slice(i, i + batchSize);
			const progress = ((i + batch.length) / entriesToProcess.length * 100).toFixed(1);
			
			console.log(`📝 Processing batch ${Math.floor(i / batchSize) + 1} (${progress}%)`);
			
			if (!options.dryRun) {
				await this.processBatch(batch, synsetData, pos);
			}
		}
	}

	private async parseIndexFile(filePath: string): Promise<any[]> {
		const content = await fs.readFile(filePath, 'utf-8');
		const lines = content.split('\n');
		
		const entries = [];
		for (const line of lines) {
			// Skip copyright and empty lines
			if (line.startsWith('  ') || !line.trim()) continue;
			
			const parts = line.split(' ').filter(p => p);
			if (parts.length < 4) continue;
			
			const [lemma, pos, synsetCnt, pCnt, ...rest] = parts;
			
			// Extract synset offsets (last synsetCnt items)
			const synsetCount = parseInt(synsetCnt);
			const offsets = rest.slice(-synsetCount);
			
			entries.push({
				lemma,
				pos,
				synsetCount,
				offsets
			});
		}
		
		return entries;
	}

	private async parseDataFile(filePath: string): Promise<Record<string, SynsetData>> {
		const content = await fs.readFile(filePath, 'utf-8');
		const lines = content.split('\n');
		
		const synsets: Record<string, SynsetData> = {};
		
		for (const line of lines) {
			// Skip copyright and empty lines
			if (line.startsWith('  ') || !line.trim()) continue;
			
			const synset = this.parseSynsetLine(line);
			if (synset) {
				synsets[synset.offset] = synset;
			}
		}
		
		return synsets;
	}

	private parseSynsetLine(line: string): SynsetData | null {
		try {
			const parts = line.split(' ');
			if (parts.length < 6) return null;
			
			const offset = parts[0];
			const lexFileNum = parts[1];
			const ssType = parts[2];
			const wCnt = parseInt(parts[3], 16); // Hexadecimal
			
			// Parse words
			const words = [];
			let idx = 4;
			for (let i = 0; i < wCnt; i++) {
				const word = parts[idx++];
				const lexId = parts[idx++];
				words.push({ word, lexId });
			}
			
			// Parse pointer count
			const pCnt = parseInt(parts[idx++]);
			
			// Parse pointers
			const pointers = [];
			for (let i = 0; i < pCnt; i++) {
				const symbol = parts[idx++];
				const ptrOffset = parts[idx++];
				const ptrPos = parts[idx++];
				const sourceTarget = parts[idx++];
				pointers.push({ symbol, offset: ptrOffset, pos: ptrPos, sourceTarget });
			}
			
			// Find gloss (after |)
			const glossIndex = line.indexOf('|');
			const gloss = glossIndex !== -1 ? line.substring(glossIndex + 1).trim() : '';
			
			return {
				offset,
				lexFileNum,
				ssType,
				words,
				pointers,
				gloss
			};
		} catch (error) {
			console.warn('Failed to parse synset line:', line.substring(0, 50) + '...');
			return null;
		}
	}

	private async processBatch(
		indexEntries: any[], 
		synsetData: Record<string, SynsetData>, 
		pos: string
	): Promise<void> {
		const wordNetEntries: WordNetEntry[] = [];
		
		for (const entry of indexEntries) {
			const wordNetEntry = this.createWordNetEntry(entry, synsetData, pos);
			if (wordNetEntry) {
				wordNetEntries.push(wordNetEntry);
			}
		}
		
		// Save to database
		await this.saveWordNetEntries(wordNetEntries);
	}

	private createWordNetEntry(
		indexEntry: any, 
		synsetData: Record<string, SynsetData>, 
		pos: string
	): WordNetEntry | null {
		try {
			const synsets: string[] = [];
			const hypernyms: string[] = [];
			const hyponyms: string[] = [];
			const holonyms: string[] = [];
			const meronyms: string[] = [];
			
			// Process each synset for this word
			for (const offset of indexEntry.offsets) {
				const synset = synsetData[offset];
				if (!synset) continue;
				
				// Add gloss as synset definition
				if (synset.gloss) {
					synsets.push(synset.gloss);
				}
				
				// Process pointers to extract relationships
				for (const pointer of synset.pointers) {
					const targetSynset = synsetData[pointer.offset];
					if (!targetSynset) continue;
					
					const targetWords = targetSynset.words.map(w => w.word.replace(/_/g, ' '));
					
					switch (pointer.symbol) {
						case '@': // Hypernym
							hypernyms.push(...targetWords);
							break;
						case '~': // Hyponym
							hyponyms.push(...targetWords);
							break;
						case '#m':
						case '#s':
						case '#p': // Holonyms
							holonyms.push(...targetWords);
							break;
						case '%m':
						case '%s':
						case '%p': // Meronyms
							meronyms.push(...targetWords);
							break;
					}
				}
			}
			
			// Create basic definition
			const partOfSpeech = this.posMapping[pos.charAt(0)] || PartsOfSpeech.NOUN;
			const definition = {
				pos: [partOfSpeech],
				ipa: '',
				explains: synsets.slice(0, 3).map(s => ({ EN: s, VI: '' })),
				examples: []
			};
			
			return {
				term: indexEntry.lemma.replace(/_/g, ' '),
				pos: partOfSpeech,
				synsets: [...new Set(synsets)],
				lemma: indexEntry.lemma,
				hypernyms: [...new Set(hypernyms)],
				hyponyms: [...new Set(hyponyms)],
				holonyms: [...new Set(holonyms)],
				meronyms: [...new Set(meronyms)],
				definitions: [definition]
			};
		} catch (error) {
			console.warn(`Failed to create WordNet entry for ${indexEntry.lemma}:`, error);
			return null;
		}
	}

	private async saveWordNetEntries(entries: WordNetEntry[]): Promise<void> {
		for (const entry of entries) {
			try {
				// Create or update word
				const word = await this.prisma.word.upsert({
					where: {
						term_language: {
							term: entry.term,
							language: Language.EN
						}
					},
					update: {},
					create: {
						term: entry.term,
						language: Language.EN
					}
				});
				
				// Create or update WordNet data
				await this.prisma.wordNetData.upsert({
					where: { word_id: word.id },
					update: {
						synsets: entry.synsets,
						lemma: entry.lemma,
						hypernyms: entry.hypernyms,
						hyponyms: entry.hyponyms,
						holonyms: entry.holonyms,
						meronyms: entry.meronyms
					},
					create: {
						word_id: word.id,
						synsets: entry.synsets,
						lemma: entry.lemma,
						hypernyms: entry.hypernyms,
						hyponyms: entry.hyponyms,
						holonyms: entry.holonyms,
						meronyms: entry.meronyms
					}
				});
				
				// Create definition if word doesn't have any
				const existingDefinitions = await this.prisma.definition.count({
					where: { word_id: word.id }
				});
				
				if (existingDefinitions === 0 && entry.definitions.length > 0) {
					const def = entry.definitions[0];
					await this.prisma.definition.create({
						data: {
							word_id: word.id,
							pos: def.pos,
							ipa: def.ipa,
							explains: {
								create: def.explains.map(explain => ({
									EN: explain.EN,
									VI: explain.VI
								}))
							}
						}
					});
				}
				
			} catch (error) {
				console.warn(`Failed to save word "${entry.term}":`, error);
			}
		}
	}
}

// CLI interface
async function main() {
	const args = process.argv.slice(2);
	const options: LoadOptions = {
		pos: args.find(arg => arg.startsWith('--pos='))?.split('=')[1],
		maxWords: parseInt(args.find(arg => arg.startsWith('--max-words='))?.split('=')[1] || '0') || undefined,
		batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1] || '100'),
		dryRun: args.includes('--dry-run'),
		force: args.includes('--force'),
		stats: args.includes('--stats')
	};
	
	if (args.includes('--help') || args.includes('-h')) {
		console.log(`
WordNet Database Loader

Usage: tsx scripts/load-wordnet-database.ts [options]

Options:
  --pos=<pos>           Load specific part of speech (noun, verb, adj, adv)
  --max-words=<count>   Maximum words to process (for testing)
  --batch-size=<size>   Batch size for processing (default: 100)
  --dry-run            Run without saving to database
  --force              Force reload even if data exists
  --stats              Show statistics only
  --help, -h           Show this help message

Examples:
  tsx scripts/load-wordnet-database.ts --stats
  tsx scripts/load-wordnet-database.ts --pos=noun --max-words=1000
  tsx scripts/load-wordnet-database.ts --dry-run
		`);
		return;
	}
	
	const loader = new WordNetDatabaseLoader();
	await loader.load(options);
}

if (require.main === module) {
	main().catch(console.error);
}

export { WordNetDatabaseLoader };
