# WordNet Scripts

This directory contains comprehensive scripts for downloading, setting up, and managing WordNet data integration in the vocabulary application.

## Quick Start

### One-Command Setup (Recommended)

```bash
# Quick setup with 1000 common words (10-15 minutes)
yarn wordnet-complete-setup:quick

# Or full setup with all WordNet data (2-4 hours)
yarn wordnet-complete-setup:full
```

### Manual Step-by-Step

```bash
# 1. Install dependencies
yarn install-wordnet-deps

# 2. Download WordNet files
yarn download-wordnet

# 3. Load data into database
yarn setup-wordnet:quick

# 4. Test the setup
yarn test-wordnet-setup
```

## Available Scripts

### Complete Setup
- `yarn wordnet-complete-setup` - Complete automated setup (quick mode)
- `yarn wordnet-complete-setup:quick` - Quick setup (1000 words)
- `yarn wordnet-complete-setup:full` - Full setup (~155k words)
- `yarn wordnet-complete-setup:test` - Test existing setup
- `yarn wordnet-complete-setup:status` - Show current status

### Individual Components
- `yarn install-wordnet-deps` - Install required dependencies
- `yarn download-wordnet` - Download WordNet files
- `yarn setup-wordnet` - Setup with options
- `yarn setup-wordnet:quick` - Quick setup
- `yarn setup-wordnet:full` - Full setup
- `yarn setup-wordnet:stats` - Show statistics
- `yarn test-wordnet-setup` - Test complete setup

### Database Loading
- `yarn load-wordnet-database` - Load WordNet data
- `yarn load-wordnet-database:stats` - Show statistics
- `yarn load-wordnet-database:noun` - Load nouns only
- `yarn load-wordnet-database:verb` - Load verbs only
- `yarn load-wordnet-database:adj` - Load adjectives only
- `yarn load-wordnet-database:adv` - Load adverbs only
- `yarn load-wordnet-database:test` - Test with 100 words

## Script Files

### Core Scripts
- `wordnet-complete-setup.ts` - Master setup orchestrator
- `install-wordnet-deps.ts` - Dependency installer
- `download-wordnet.ts` - WordNet file downloader
- `load-wordnet-database.ts` - Database loader and parser
- `setup-wordnet.ts` - Setup orchestrator
- `test-wordnet-setup.ts` - Comprehensive tester

### Features
- **Automated downloads** from Princeton University
- **Intelligent parsing** of WordNet database files
- **Batch processing** for optimal performance
- **Progress tracking** and statistics
- **Error handling** and recovery
- **Comprehensive testing** and verification

## Usage Examples

### Development Setup
```bash
# Quick start for development
yarn wordnet-complete-setup:quick

# Check what was loaded
yarn setup-wordnet:stats

# Test functionality
yarn test-wordnet-setup
```

### Production Setup
```bash
# Full WordNet database
yarn wordnet-complete-setup:full

# Monitor progress
yarn setup-wordnet:stats

# Verify everything works
yarn test-wordnet-setup
```

### Custom Loading
```bash
# Load specific amounts
yarn load-wordnet-database --pos=noun --max-words=5000
yarn load-wordnet-database --pos=verb --max-words=1000

# Test without saving
yarn load-wordnet-database --pos=noun --max-words=100 --dry-run
```

### Troubleshooting
```bash
# Check current status
yarn wordnet-complete-setup:status

# Test individual components
yarn test-wordnet-setup --verbose

# Force re-download
yarn download-wordnet --force

# Force reload data
yarn load-wordnet-database --force
```

## File Structure

After running the scripts, WordNet data is organized as:

```
data/
└── wordnet/
    ├── downloads/          # Temporary files (auto-cleaned)
    └── extracted/          # WordNet database files
        ├── data.noun       # Noun synset data
        ├── data.verb       # Verb synset data
        ├── data.adj        # Adjective synset data
        ├── data.adv        # Adverb synset data
        ├── index.noun      # Noun index
        ├── index.verb      # Verb index
        ├── index.adj       # Adjective index
        ├── index.adv       # Adverb index
        └── *.exc           # Exception lists
```

## Performance

### Loading Times
- **Dependencies**: 2-5 minutes
- **Download**: 2-5 minutes
- **Quick setup**: 5-10 minutes (1000 words)
- **Full setup**: 2-4 hours (155k words)

### Database Impact
- **Quick setup**: ~50 MB database size
- **Full setup**: ~500 MB - 1 GB database size
- **Query performance**: <100ms for typical lookups

## Requirements

### System Requirements
- Node.js 18+ with TypeScript support
- PostgreSQL database
- Internet connection for downloads
- ~1 GB free disk space for full setup

### Dependencies (Auto-installed)
- `unzipper` - File extraction
- `natural` - Natural language processing
- `@prisma/client` - Database access
- TypeScript types for all packages

## Troubleshooting

### Common Issues
1. **Download fails**: Check internet connection, try `--force`
2. **Database errors**: Ensure PostgreSQL is running, check `DATABASE_URL`
3. **Permission errors**: Check file/directory permissions
4. **Memory issues**: Use smaller batch sizes, load one POS at a time

### Getting Help
1. Run `yarn test-wordnet-setup --verbose` for diagnostics
2. Check `docs/WORDNET_SETUP_GUIDE.md` for detailed guide
3. Review console output for specific error messages
4. Use `--dry-run` to test without making changes

## Integration

Once setup is complete, WordNet data is automatically available in your application:

```typescript
// Search words with WordNet data
const words = await wordService.searchWordsWithWordNet('house');

// Get WordNet information
const wordNetInfo = await wordNetService.getWordNetInfo('house', Language.EN);

// Display in UI
<WordNetInfo wordNetData={word.wordnet_data} term={word.term} />
```

## Maintenance

### Regular Tasks
- Monitor database size and performance
- Update WordNet data as needed
- Run tests after system updates
- Backup WordNet data before major changes

### Useful Commands
```bash
# Check current statistics
yarn setup-wordnet:stats

# Test functionality
yarn test-wordnet-setup

# Add more data
yarn load-wordnet-database --pos=verb --max-words=2000

# Verify integrity
yarn wordnet-complete-setup:test
```

For detailed documentation, see `docs/WORDNET_SETUP_GUIDE.md`.
