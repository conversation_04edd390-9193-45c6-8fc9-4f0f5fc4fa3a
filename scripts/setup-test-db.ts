#!/usr/bin/env tsx

/**
 * Test Database Setup Script
 * 
 * This script sets up the test database for running tests.
 * It creates a separate test database and runs migrations.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { PrismaClient } from '@prisma/client';

const execAsync = promisify(exec);

async function setupTestDatabase() {
	console.log('🚀 Setting up test database...');

	try {
		// 1. Ensure Docker containers are running
		console.log('📦 Starting database containers...');
		await execAsync('yarn dup');

		// Wait for database to be ready
		console.log('⏳ Waiting for database to be ready...');
		await new Promise((resolve) => setTimeout(resolve, 5000));

		// 2. Set environment to test
		process.env.NODE_ENV = 'test';
		process.env.DATABASE_URL = 'postgresql://postgres:password@localhost:5432/vocab_test?schema=public';

		// 3. Create test database if it doesn't exist
		console.log('🗄️ Creating test database...');
		try {
			await execAsync('createdb -h localhost -U postgres vocab_test');
		} catch (error) {
			// Database might already exist, that's okay
			console.log('📝 Test database already exists or creation failed (this might be okay)');
		}

		// 4. Run migrations on test database
		console.log('🔄 Running migrations on test database...');
		await execAsync('DATABASE_URL="postgresql://postgres:password@localhost:5432/vocab_test?schema=public" yarn p:m');

		// 5. Verify database connection
		console.log('🔍 Verifying database connection...');
		const prisma = new PrismaClient({
			datasources: {
				db: {
					url: 'postgresql://postgres:password@localhost:5432/vocab_test?schema=public',
				},
			},
		});

		await prisma.$connect();
		console.log('✅ Database connection verified');
		await prisma.$disconnect();

		console.log('🎉 Test database setup completed successfully!');
		console.log('');
		console.log('You can now run tests with:');
		console.log('  yarn test');
		console.log('  yarn test:unit');
		console.log('  yarn test:e2e');

	} catch (error) {
		console.error('❌ Test database setup failed:', error);
		process.exit(1);
	}
}

// Run setup if this script is executed directly
if (require.main === module) {
	setupTestDatabase();
}

export { setupTestDatabase };
