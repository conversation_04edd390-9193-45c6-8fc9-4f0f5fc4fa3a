#!/usr/bin/env tsx

/**
 * Comprehensive Test Runner Sc<PERSON>t
 * 
 * This script runs all types of tests in the correct order and generates
 * a comprehensive test report with coverage metrics.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import path from 'path';

const execAsync = promisify(exec);

interface TestResult {
	name: string;
	status: 'passed' | 'failed' | 'skipped';
	duration: number;
	coverage?: {
		lines: number;
		functions: number;
		branches: number;
		statements: number;
	};
	errors?: string[];
}

interface TestSuite {
	name: string;
	command: string;
	timeout: number;
	required: boolean;
	description: string;
}

const testSuites: TestSuite[] = [
	{
		name: 'Unit Tests (Vitest)',
		command: 'yarn test:unit:coverage',
		timeout: 120000, // 2 minutes
		required: true,
		description: 'Fast unit tests with Vitest',
	},
	{
		name: 'Unit Tests (Jest)',
		command: 'yarn test:coverage',
		timeout: 180000, // 3 minutes
		required: true,
		description: 'Comprehensive unit and integration tests',
	},
	{
		name: 'Performance Tests',
		command: 'yarn test:performance',
		timeout: 300000, // 5 minutes
		required: false,
		description: 'Performance and load testing',
	},
	{
		name: 'Security Tests',
		command: 'yarn test:security',
		timeout: 180000, // 3 minutes
		required: true,
		description: 'Security vulnerability testing',
	},
	{
		name: 'E2E Tests',
		command: 'yarn test:e2e',
		timeout: 600000, // 10 minutes
		required: false,
		description: 'End-to-end browser testing',
	},
];

async function runTestSuite(suite: TestSuite): Promise<TestResult> {
	console.log(`\n🧪 Running ${suite.name}...`);
	console.log(`📝 ${suite.description}`);
	
	const startTime = Date.now();
	
	try {
		const { stdout, stderr } = await execAsync(suite.command, {
			timeout: suite.timeout,
			maxBuffer: 1024 * 1024 * 10, // 10MB buffer
		});
		
		const endTime = Date.now();
		const duration = endTime - startTime;
		
		console.log(`✅ ${suite.name} completed in ${duration}ms`);
		
		// Parse coverage information if available
		let coverage;
		if (stdout.includes('Coverage')) {
			coverage = parseCoverage(stdout);
		}
		
		return {
			name: suite.name,
			status: 'passed',
			duration,
			coverage,
		};
		
	} catch (error: any) {
		const endTime = Date.now();
		const duration = endTime - startTime;
		
		console.error(`❌ ${suite.name} failed after ${duration}ms`);
		console.error(`Error: ${error.message}`);
		
		return {
			name: suite.name,
			status: 'failed',
			duration,
			errors: [error.message],
		};
	}
}

function parseCoverage(output: string): TestResult['coverage'] {
	// Simple coverage parsing - in real implementation, parse actual coverage reports
	const lines = output.match(/Lines\s*:\s*(\d+\.?\d*)%/);
	const functions = output.match(/Functions\s*:\s*(\d+\.?\d*)%/);
	const branches = output.match(/Branches\s*:\s*(\d+\.?\d*)%/);
	const statements = output.match(/Statements\s*:\s*(\d+\.?\d*)%/);
	
	return {
		lines: lines ? parseFloat(lines[1]) : 0,
		functions: functions ? parseFloat(functions[1]) : 0,
		branches: branches ? parseFloat(branches[1]) : 0,
		statements: statements ? parseFloat(statements[1]) : 0,
	};
}

function generateReport(results: TestResult[]): string {
	const totalDuration = results.reduce((sum, result) => sum + result.duration, 0);
	const passedTests = results.filter(r => r.status === 'passed').length;
	const failedTests = results.filter(r => r.status === 'failed').length;
	const skippedTests = results.filter(r => r.status === 'skipped').length;
	
	let report = `# Comprehensive Test Report\n\n`;
	report += `**Generated:** ${new Date().toISOString()}\n`;
	report += `**Total Duration:** ${(totalDuration / 1000).toFixed(2)}s\n\n`;
	
	report += `## Summary\n\n`;
	report += `- ✅ **Passed:** ${passedTests}\n`;
	report += `- ❌ **Failed:** ${failedTests}\n`;
	report += `- ⏭️ **Skipped:** ${skippedTests}\n`;
	report += `- 📊 **Total:** ${results.length}\n\n`;
	
	report += `## Test Results\n\n`;
	
	results.forEach(result => {
		const statusIcon = result.status === 'passed' ? '✅' : result.status === 'failed' ? '❌' : '⏭️';
		report += `### ${statusIcon} ${result.name}\n\n`;
		report += `- **Status:** ${result.status}\n`;
		report += `- **Duration:** ${(result.duration / 1000).toFixed(2)}s\n`;
		
		if (result.coverage) {
			report += `- **Coverage:**\n`;
			report += `  - Lines: ${result.coverage.lines.toFixed(1)}%\n`;
			report += `  - Functions: ${result.coverage.functions.toFixed(1)}%\n`;
			report += `  - Branches: ${result.coverage.branches.toFixed(1)}%\n`;
			report += `  - Statements: ${result.coverage.statements.toFixed(1)}%\n`;
		}
		
		if (result.errors && result.errors.length > 0) {
			report += `- **Errors:**\n`;
			result.errors.forEach(error => {
				report += `  - ${error}\n`;
			});
		}
		
		report += `\n`;
	});
	
	// Overall coverage summary
	const coverageResults = results.filter(r => r.coverage);
	if (coverageResults.length > 0) {
		const avgCoverage = {
			lines: coverageResults.reduce((sum, r) => sum + (r.coverage?.lines || 0), 0) / coverageResults.length,
			functions: coverageResults.reduce((sum, r) => sum + (r.coverage?.functions || 0), 0) / coverageResults.length,
			branches: coverageResults.reduce((sum, r) => sum + (r.coverage?.branches || 0), 0) / coverageResults.length,
			statements: coverageResults.reduce((sum, r) => sum + (r.coverage?.statements || 0), 0) / coverageResults.length,
		};
		
		report += `## Overall Coverage\n\n`;
		report += `- **Lines:** ${avgCoverage.lines.toFixed(1)}%\n`;
		report += `- **Functions:** ${avgCoverage.functions.toFixed(1)}%\n`;
		report += `- **Branches:** ${avgCoverage.branches.toFixed(1)}%\n`;
		report += `- **Statements:** ${avgCoverage.statements.toFixed(1)}%\n\n`;
	}
	
	report += `## Recommendations\n\n`;
	
	if (failedTests > 0) {
		report += `- 🔧 **Fix failing tests** before deploying to production\n`;
	}
	
	const lowCoverageTests = results.filter(r => 
		r.coverage && (
			r.coverage.lines < 70 || 
			r.coverage.functions < 70 || 
			r.coverage.branches < 70 || 
			r.coverage.statements < 70
		)
	);
	
	if (lowCoverageTests.length > 0) {
		report += `- 📈 **Improve test coverage** for: ${lowCoverageTests.map(t => t.name).join(', ')}\n`;
	}
	
	const slowTests = results.filter(r => r.duration > 60000); // > 1 minute
	if (slowTests.length > 0) {
		report += `- ⚡ **Optimize slow tests:** ${slowTests.map(t => t.name).join(', ')}\n`;
	}
	
	return report;
}

async function main() {
	console.log('🚀 Starting comprehensive test suite...\n');
	
	// Ensure test results directory exists
	const resultsDir = path.join(process.cwd(), 'test-results');
	if (!existsSync(resultsDir)) {
		mkdirSync(resultsDir, { recursive: true });
	}
	
	const results: TestResult[] = [];
	let hasFailures = false;
	
	// Run test suites sequentially
	for (const suite of testSuites) {
		const result = await runTestSuite(suite);
		results.push(result);
		
		if (result.status === 'failed' && suite.required) {
			hasFailures = true;
			console.log(`\n⚠️ Required test suite failed: ${suite.name}`);
		}
	}
	
	// Generate and save report
	const report = generateReport(results);
	const reportPath = path.join(resultsDir, 'comprehensive-test-report.md');
	writeFileSync(reportPath, report);
	
	console.log(`\n📊 Test report generated: ${reportPath}`);
	console.log('\n' + '='.repeat(80));
	console.log(report);
	console.log('='.repeat(80));
	
	// Exit with appropriate code
	if (hasFailures) {
		console.log('\n❌ Some required tests failed. Please fix before proceeding.');
		process.exit(1);
	} else {
		console.log('\n✅ All tests completed successfully!');
		process.exit(0);
	}
}

// Run if this script is executed directly
if (require.main === module) {
	main().catch(error => {
		console.error('❌ Test runner failed:', error);
		process.exit(1);
	});
}

export { main as runAllTests };
