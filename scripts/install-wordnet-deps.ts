#!/usr/bin/env tsx

/**
 * WordNet Dependencies Installation Script
 *
 * This script installs and configures all necessary dependencies for WordNet integration.
 * It ensures that all required packages are available for downloading, parsing, and
 * loading WordNet data.
 *
 * Usage:
 *   yarn install-wordnet-deps
 *   tsx scripts/install-wordnet-deps.ts
 *
 * Features:
 * - Installs required npm packages
 * - Verifies installation
 * - Sets up directory structure
 * - Provides troubleshooting information
 */

import { execSync } from 'child_process';
import fs from 'fs/promises';
import path from 'path';

interface DependencyInfo {
	name: string;
	version?: string;
	dev?: boolean;
	description: string;
	required: boolean;
}

class WordNetDependencyInstaller {
	private readonly dependencies: DependencyInfo[] = [
		{
			name: 'unzipper',
			description: 'For extracting tar.gz files',
			required: true,
		},
		{
			name: 'natural',
			description: 'Natural language processing library with WordNet support',
			required: true,
		},
		{
			name: '@types/natural',
			dev: true,
			description: 'TypeScript types for natural',
			required: true,
		},
		{
			name: 'wordnet-db',
			description: 'WordNet database files package (alternative source)',
			required: false,
		},
	];

	async install(): Promise<void> {
		console.log('🚀 Installing WordNet dependencies...\n');

		try {
			// Check current package.json
			await this.checkPackageJson();

			// Install required dependencies
			await this.installDependencies();

			// Verify installations
			await this.verifyInstallations();

			// Setup directory structure
			await this.setupDirectories();

			// Show completion message
			this.showCompletionMessage();
		} catch (error) {
			console.error('❌ Error installing WordNet dependencies:', error);
			this.showTroubleshootingTips();
			throw error;
		}
	}

	private async checkPackageJson(): Promise<void> {
		console.log('📋 Checking package.json...');

		try {
			const packageJsonPath = path.join(process.cwd(), 'package.json');
			const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));

			console.log(`✅ Project: ${packageJson.name} v${packageJson.version}`);

			// Check existing dependencies
			const existing = {
				dependencies: packageJson.dependencies || {},
				devDependencies: packageJson.devDependencies || {},
			};

			for (const dep of this.dependencies) {
				const isInstalled =
					existing.dependencies[dep.name] || existing.devDependencies[dep.name];

				if (isInstalled) {
					console.log(`✅ ${dep.name}: ${isInstalled} (already installed)`);
				} else if (dep.required) {
					console.log(`⚠️  ${dep.name}: Not installed (required)`);
				} else {
					console.log(`ℹ️  ${dep.name}: Not installed (optional)`);
				}
			}
		} catch (error) {
			throw new Error('Could not read package.json. Make sure you are in the project root.');
		}
	}

	private async installDependencies(): Promise<void> {
		console.log('\n📦 Installing dependencies...');

		// Separate required and optional dependencies
		const requiredDeps = this.dependencies.filter((d) => d.required);
		const optionalDeps = this.dependencies.filter((d) => !d.required);

		// Install required dependencies
		if (requiredDeps.length > 0) {
			console.log('📦 Installing required dependencies...');
			await this.installDependencyGroup(requiredDeps);
		}

		// Install optional dependencies (with error handling)
		if (optionalDeps.length > 0) {
			console.log('📦 Installing optional dependencies...');
			for (const dep of optionalDeps) {
				try {
					await this.installDependencyGroup([dep]);
				} catch (error) {
					console.warn(`⚠️  Could not install optional dependency ${dep.name}:`, error);
				}
			}
		}
	}

	private async installDependencyGroup(deps: DependencyInfo[]): Promise<void> {
		const prodDeps = deps
			.filter((d) => !d.dev)
			.map((d) => (d.version ? `${d.name}@${d.version}` : d.name));
		const devDeps = deps
			.filter((d) => d.dev)
			.map((d) => (d.version ? `${d.name}@${d.version}` : d.name));

		// Install production dependencies
		if (prodDeps.length > 0) {
			console.log(`📦 Installing: ${prodDeps.join(', ')}`);
			try {
				execSync(`yarn add ${prodDeps.join(' ')}`, {
					stdio: 'inherit',
					cwd: process.cwd(),
				});
				console.log('✅ Production dependencies installed');
			} catch (error) {
				throw new Error(
					`Failed to install production dependencies: ${prodDeps.join(', ')}`
				);
			}
		}

		// Install development dependencies
		if (devDeps.length > 0) {
			console.log(`📦 Installing dev: ${devDeps.join(', ')}`);
			try {
				execSync(`yarn add -D ${devDeps.join(' ')}`, {
					stdio: 'inherit',
					cwd: process.cwd(),
				});
				console.log('✅ Development dependencies installed');
			} catch (error) {
				throw new Error(
					`Failed to install development dependencies: ${devDeps.join(', ')}`
				);
			}
		}
	}

	private async verifyInstallations(): Promise<void> {
		console.log('\n🔍 Verifying installations...');

		for (const dep of this.dependencies) {
			try {
				// Try to require the package
				require.resolve(dep.name);
				console.log(`✅ ${dep.name}: Available`);
			} catch (error) {
				if (dep.required) {
					console.error(`❌ ${dep.name}: Not available (required)`);
					throw new Error(`Required dependency ${dep.name} is not available`);
				} else {
					console.warn(`⚠️  ${dep.name}: Not available (optional)`);
				}
			}
		}
	}

	private async setupDirectories(): Promise<void> {
		console.log('\n📁 Setting up directory structure...');

		const directories = [
			'data',
			'data/wordnet',
			'data/wordnet/downloads',
			'data/wordnet/extracted',
		];

		for (const dir of directories) {
			const dirPath = path.join(process.cwd(), dir);
			try {
				await fs.mkdir(dirPath, { recursive: true });
				console.log(`✅ Created: ${dir}/`);
			} catch (error) {
				console.warn(`⚠️  Could not create directory ${dir}:`, error);
			}
		}

		// Create .gitignore entry for data directory
		await this.updateGitignore();
	}

	private async updateGitignore(): Promise<void> {
		const gitignorePath = path.join(process.cwd(), '.gitignore');

		try {
			let gitignoreContent = '';
			try {
				gitignoreContent = await fs.readFile(gitignorePath, 'utf-8');
			} catch {
				// .gitignore doesn't exist, create it
			}

			const wordnetIgnoreEntry = '\n# WordNet data files\ndata/wordnet/\n';

			if (!gitignoreContent.includes('data/wordnet/')) {
				gitignoreContent += wordnetIgnoreEntry;
				await fs.writeFile(gitignorePath, gitignoreContent);
				console.log('✅ Updated .gitignore to exclude WordNet data files');
			} else {
				console.log('✅ .gitignore already configured for WordNet data');
			}
		} catch (error) {
			console.warn('⚠️  Could not update .gitignore:', error);
		}
	}

	private showCompletionMessage(): void {
		console.log('\n🎉 WordNet dependencies installation completed!\n');

		console.log('📚 Next steps:');
		console.log('1. Download WordNet data:');
		console.log('   yarn download-wordnet');
		console.log('');
		console.log('2. Load WordNet into database (quick start):');
		console.log('   yarn setup-wordnet:quick');
		console.log('');
		console.log('3. Or load complete WordNet database:');
		console.log('   yarn setup-wordnet:full');
		console.log('');
		console.log('4. Check current status:');
		console.log('   yarn setup-wordnet:stats');
		console.log('');

		console.log('📖 Available scripts:');
		console.log('   yarn download-wordnet          # Download WordNet files');
		console.log('   yarn load-wordnet-database     # Load data into database');
		console.log('   yarn setup-wordnet             # Complete setup process');
		console.log('   yarn setup-wordnet:quick       # Quick setup (1000 words)');
		console.log('   yarn setup-wordnet:full        # Full setup (~155k words)');
		console.log('   yarn setup-wordnet:stats       # Show statistics');
	}

	private showTroubleshootingTips(): void {
		console.log('\n🔧 Troubleshooting Tips:\n');

		console.log('1. Network Issues:');
		console.log('   - Check your internet connection');
		console.log('   - Try using a different network or VPN');
		console.log('   - Some corporate networks block npm registry');
		console.log('');

		console.log('2. Permission Issues:');
		console.log('   - Make sure you have write permissions in the project directory');
		console.log('   - On Unix systems, avoid using sudo with npm/yarn');
		console.log('');

		console.log('3. Package Manager Issues:');
		console.log('   - Make sure you are using Yarn (not npm)');
		console.log('   - Try clearing yarn cache: yarn cache clean');
		console.log('   - Try removing node_modules and yarn.lock, then yarn install');
		console.log('');

		console.log('4. TypeScript Issues:');
		console.log('   - Make sure TypeScript is installed: yarn add -D typescript');
		console.log('   - Make sure tsx is available: yarn add -D tsx');
		console.log('');

		console.log('5. Database Issues:');
		console.log('   - Make sure PostgreSQL is running');
		console.log('   - Check DATABASE_URL in .env.local');
		console.log('   - Run database migrations: yarn p:m');
	}
}

// CLI interface
async function main() {
	const args = process.argv.slice(2);

	if (args.includes('--help') || args.includes('-h')) {
		console.log(`
WordNet Dependencies Installation Script

Usage: tsx scripts/install-wordnet-deps.ts

This script installs all necessary dependencies for WordNet integration:
- unzipper: For extracting compressed files
- natural: Natural language processing with WordNet support
- wordnet-db: Alternative WordNet data source
- TypeScript types for all packages

The script also:
- Sets up the required directory structure
- Updates .gitignore to exclude data files
- Provides next steps and troubleshooting information

Examples:
  tsx scripts/install-wordnet-deps.ts
  yarn install-wordnet-deps
		`);
		return;
	}

	const installer = new WordNetDependencyInstaller();
	await installer.install();
}

if (require.main === module) {
	main().catch(console.error);
}

export { WordNetDependencyInstaller };
