#!/usr/bin/env tsx

/**
 * WordNet Complete Setup Script
 * 
 * This is the master script that orchestrates the complete WordNet setup process
 * from dependency installation to final testing. It provides a one-command solution
 * for setting up WordNet integration in the vocabulary application.
 * 
 * Usage:
 *   yarn wordnet-complete-setup
 *   tsx scripts/wordnet-complete-setup.ts [mode]
 * 
 * Modes:
 *   quick    - Quick setup with 1000 words (default)
 *   full     - Complete setup with all WordNet data
 *   test     - Test setup only
 *   deps     - Install dependencies only
 * 
 * Features:
 * - Automated dependency installation
 * - Progressive setup with status tracking
 * - Comprehensive error handling
 * - Final verification and testing
 * - Detailed progress reporting
 */

import { execSync } from 'child_process';
import { WordNetDependencyInstaller } from './install-wordnet-deps';
import { WordNetSetup } from './setup-wordnet';
import { WordNetSetupTester } from './test-wordnet-setup';

interface SetupMode {
	name: string;
	description: string;
	steps: string[];
	estimatedTime: string;
}

class WordNetCompleteSetup {
	private readonly modes: Record<string, SetupMode> = {
		quick: {
			name: 'Quick Setup',
			description: 'Install dependencies and load 1000 common words',
			steps: ['deps', 'download', 'load-quick', 'test'],
			estimatedTime: '10-15 minutes'
		},
		full: {
			name: 'Full Setup',
			description: 'Complete WordNet database with ~155k words',
			steps: ['deps', 'download', 'load-full', 'test'],
			estimatedTime: '2-4 hours'
		},
		test: {
			name: 'Test Only',
			description: 'Test existing WordNet setup',
			steps: ['test'],
			estimatedTime: '2-3 minutes'
		},
		deps: {
			name: 'Dependencies Only',
			description: 'Install required dependencies',
			steps: ['deps'],
			estimatedTime: '2-5 minutes'
		}
	};

	async run(mode: string = 'quick'): Promise<void> {
		const setupMode = this.modes[mode];
		if (!setupMode) {
			console.error(`❌ Unknown mode: ${mode}`);
			this.showAvailableModes();
			return;
		}

		console.log('🚀 WordNet Complete Setup\n');
		console.log(`📋 Mode: ${setupMode.name}`);
		console.log(`📝 Description: ${setupMode.description}`);
		console.log(`⏱️  Estimated time: ${setupMode.estimatedTime}\n`);

		const startTime = Date.now();

		try {
			for (let i = 0; i < setupMode.steps.length; i++) {
				const step = setupMode.steps[i];
				const stepNumber = i + 1;
				const totalSteps = setupMode.steps.length;

				console.log(`\n📍 Step ${stepNumber}/${totalSteps}: ${this.getStepName(step)}`);
				console.log('─'.repeat(50));

				await this.executeStep(step);

				console.log(`✅ Step ${stepNumber} completed\n`);
			}

			const totalTime = (Date.now() - startTime) / 1000 / 60;
			console.log('🎉 WordNet Complete Setup Finished!\n');
			console.log(`⏱️  Total time: ${totalTime.toFixed(1)} minutes`);
			console.log('📚 WordNet is now ready for use in your vocabulary application.\n');

			this.showNextSteps(mode);

		} catch (error) {
			console.error('\n❌ Setup failed:', error);
			console.log('\n🔧 Troubleshooting:');
			console.log('1. Check the error message above');
			console.log('2. Ensure you have internet connection');
			console.log('3. Verify PostgreSQL is running');
			console.log('4. Try running individual steps manually');
			console.log('5. Run: yarn test-wordnet-setup for diagnostics\n');
			throw error;
		}
	}

	private async executeStep(step: string): Promise<void> {
		switch (step) {
			case 'deps':
				await this.installDependencies();
				break;
			case 'download':
				await this.downloadWordNet();
				break;
			case 'load-quick':
				await this.loadWordNetQuick();
				break;
			case 'load-full':
				await this.loadWordNetFull();
				break;
			case 'test':
				await this.testSetup();
				break;
			default:
				throw new Error(`Unknown step: ${step}`);
		}
	}

	private async installDependencies(): Promise<void> {
		console.log('📦 Installing WordNet dependencies...');
		const installer = new WordNetDependencyInstaller();
		await installer.install();
	}

	private async downloadWordNet(): Promise<void> {
		console.log('📥 Downloading WordNet files...');
		try {
			execSync('tsx scripts/download-wordnet.ts', { 
				stdio: 'inherit',
				cwd: process.cwd()
			});
		} catch (error) {
			throw new Error('Failed to download WordNet files');
		}
	}

	private async loadWordNetQuick(): Promise<void> {
		console.log('📚 Loading WordNet data (quick setup)...');
		const setup = new WordNetSetup();
		await setup.quickStart();
	}

	private async loadWordNetFull(): Promise<void> {
		console.log('📚 Loading complete WordNet database...');
		const setup = new WordNetSetup();
		await setup.fullSetup();
	}

	private async testSetup(): Promise<void> {
		console.log('🧪 Testing WordNet setup...');
		const tester = new WordNetSetupTester();
		await tester.runTests({ verbose: false });
	}

	private getStepName(step: string): string {
		const stepNames: Record<string, string> = {
			'deps': 'Install Dependencies',
			'download': 'Download WordNet Files',
			'load-quick': 'Load WordNet Data (Quick)',
			'load-full': 'Load WordNet Data (Full)',
			'test': 'Test Setup'
		};
		return stepNames[step] || step;
	}

	private showAvailableModes(): void {
		console.log('\n📋 Available modes:\n');
		Object.entries(this.modes).forEach(([key, mode]) => {
			console.log(`  ${key.padEnd(8)} - ${mode.description}`);
			console.log(`${' '.repeat(12)}(${mode.estimatedTime})\n`);
		});
	}

	private showNextSteps(mode: string): void {
		console.log('🎯 Next Steps:\n');

		if (mode === 'deps') {
			console.log('1. Download WordNet files:');
			console.log('   yarn download-wordnet\n');
			console.log('2. Load WordNet data:');
			console.log('   yarn setup-wordnet:quick\n');
			console.log('3. Test the setup:');
			console.log('   yarn test-wordnet-setup\n');
		} else if (mode === 'test') {
			console.log('✅ Setup verification completed.\n');
			console.log('If tests failed, check the suggestions above.\n');
		} else {
			console.log('1. Start using WordNet in your application:');
			console.log('   - Search for words with WordNet data');
			console.log('   - Display semantic relationships');
			console.log('   - Use synsets for better definitions\n');

			console.log('2. Monitor and maintain:');
			console.log('   yarn setup-wordnet:stats     # Check statistics');
			console.log('   yarn test-wordnet-setup      # Run diagnostics\n');

			if (mode === 'quick') {
				console.log('3. Load more data if needed:');
				console.log('   yarn load-wordnet-database:verb --max-words=1000');
				console.log('   yarn setup-wordnet:full     # Complete database\n');
			}
		}

		console.log('📖 Documentation:');
		console.log('   docs/WORDNET_SETUP_GUIDE.md  # Complete guide');
		console.log('   docs/WORDNET_INTEGRATION.md  # API usage\n');
	}

	async showStatus(): Promise<void> {
		console.log('📊 WordNet Setup Status\n');

		try {
			const tester = new WordNetSetupTester();
			await tester.runTests({ 
				verbose: false,
				skipDownload: false,
				skipDatabase: false,
				skipApi: false
			});
		} catch (error) {
			console.error('❌ Status check failed:', error);
		}
	}
}

// CLI interface
async function main() {
	const args = process.argv.slice(2);
	const mode = args[0] || 'quick';

	if (args.includes('--help') || args.includes('-h')) {
		console.log(`
WordNet Complete Setup Script

Usage: tsx scripts/wordnet-complete-setup.ts [mode] [options]

Modes:
  quick    Quick setup with 1000 words (default, ~10-15 min)
  full     Complete setup with all WordNet data (~2-4 hours)
  test     Test existing setup only (~2-3 min)
  deps     Install dependencies only (~2-5 min)
  status   Show current setup status

Options:
  --help, -h    Show this help message

Examples:
  # Quick setup (recommended for development)
  tsx scripts/wordnet-complete-setup.ts quick
  
  # Full production setup
  tsx scripts/wordnet-complete-setup.ts full
  
  # Test existing setup
  tsx scripts/wordnet-complete-setup.ts test
  
  # Install dependencies only
  tsx scripts/wordnet-complete-setup.ts deps
  
  # Check current status
  tsx scripts/wordnet-complete-setup.ts status

This script automates the complete WordNet setup process:
1. Installs required dependencies (unzipper, natural, etc.)
2. Downloads WordNet 3.0 database files from Princeton
3. Extracts and organizes the data files
4. Parses and loads data into PostgreSQL database
5. Tests the complete integration

For manual control, use individual scripts:
  yarn install-wordnet-deps     # Install dependencies
  yarn download-wordnet         # Download files
  yarn setup-wordnet:quick      # Quick data loading
  yarn test-wordnet-setup       # Test setup
		`);
		return;
	}

	const setup = new WordNetCompleteSetup();

	if (mode === 'status') {
		await setup.showStatus();
	} else {
		await setup.run(mode);
	}
}

if (require.main === module) {
	main().catch(console.error);
}

export { WordNetCompleteSetup };
