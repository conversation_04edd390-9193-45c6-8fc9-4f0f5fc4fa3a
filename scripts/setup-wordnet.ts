#!/usr/bin/env tsx

/**
 * WordNet Complete Setup Script
 * 
 * This script orchestrates the complete WordNet setup process:
 * 1. Downloads WordNet database files
 * 2. Extracts and organizes the files
 * 3. Parses and loads data into PostgreSQL database
 * 4. Provides comprehensive progress tracking and statistics
 * 
 * Usage:
 *   yarn setup-wordnet
 *   tsx scripts/setup-wordnet.ts [options]
 * 
 * Features:
 * - Complete automated setup
 * - Progress tracking and statistics
 * - Flexible configuration options
 * - Error handling and recovery
 * - Performance optimization
 */

import { WordNetDownloader } from './download-wordnet';
import { WordNetDatabaseLoader } from './load-wordnet-database';

interface SetupOptions {
	// Download options
	forceDownload?: boolean;
	skipDownload?: boolean;
	
	// Loading options
	pos?: string;
	maxWords?: number;
	batchSize?: number;
	dryRun?: boolean;
	forceLoad?: boolean;
	
	// General options
	stats?: boolean;
	verbose?: boolean;
	cleanup?: boolean;
}

class WordNetSetup {
	private downloader: WordNetDownloader;
	private loader: WordNetDatabaseLoader;

	constructor() {
		this.downloader = new WordNetDownloader();
		this.loader = new WordNetDatabaseLoader();
	}

	async setup(options: SetupOptions = {}): Promise<void> {
		console.log('🚀 WordNet Complete Setup Starting...\n');
		
		try {
			// Show current statistics if requested
			if (options.stats) {
				await this.showCurrentStats();
				return;
			}
			
			// Step 1: Download WordNet files
			if (!options.skipDownload) {
				console.log('📥 Step 1: Downloading WordNet files...');
				await this.downloader.download({
					force: options.forceDownload,
					verify: true,
					cleanup: options.cleanup !== false
				});
				console.log('✅ Download completed\n');
			} else {
				console.log('⏭️  Skipping download step\n');
			}
			
			// Step 2: Load data into database
			console.log('📚 Step 2: Loading data into database...');
			await this.loader.load({
				pos: options.pos,
				maxWords: options.maxWords,
				batchSize: options.batchSize || 100,
				dryRun: options.dryRun,
				force: options.forceLoad
			});
			console.log('✅ Database loading completed\n');
			
			// Step 3: Show final statistics
			console.log('📊 Step 3: Final Statistics...');
			await this.showFinalStats();
			
			console.log('\n🎉 WordNet setup completed successfully!');
			console.log('💡 You can now use WordNet data in your vocabulary application.');
			
		} catch (error) {
			console.error('\n❌ WordNet setup failed:', error);
			console.log('\n🔧 Troubleshooting tips:');
			console.log('1. Check your internet connection for downloads');
			console.log('2. Ensure PostgreSQL database is running');
			console.log('3. Verify database connection settings');
			console.log('4. Try running with --dry-run to test without saving');
			throw error;
		}
	}

	private async showCurrentStats(): Promise<void> {
		console.log('📊 Current WordNet Statistics\n');
		
		// Show download status
		console.log('📥 Download Status:');
		try {
			const extractDir = this.downloader.getExtractDir();
			const fs = await import('fs/promises');
			await fs.access(extractDir);
			console.log('✅ WordNet files are downloaded and extracted');
			
			// List available files
			const files = await fs.readdir(extractDir);
			const dataFiles = files.filter(f => f.startsWith('data.') || f.startsWith('index.'));
			console.log(`📁 Available files: ${dataFiles.length}`);
			
			// Show file sizes
			for (const file of dataFiles.sort()) {
				try {
					const stat = await fs.stat(`${extractDir}/${file}`);
					const sizeMB = (stat.size / 1024 / 1024).toFixed(2);
					console.log(`   ${file}: ${sizeMB} MB`);
				} catch {
					console.log(`   ${file}: Error reading file`);
				}
			}
		} catch {
			console.log('❌ WordNet files not found - run download first');
		}
		
		console.log('\n📚 Database Status:');
		await this.loader.load({ stats: true });
	}

	private async showFinalStats(): Promise<void> {
		await this.loader.load({ stats: true });
		
		console.log('\n💾 Storage Information:');
		try {
			const { PrismaClient } = await import('@prisma/client');
			const prisma = new PrismaClient();
			
			// Get database size information
			const wordCount = await prisma.word.count();
			const wordNetCount = await prisma.word.count({
				where: { wordnet_data: { isNot: null } }
			});
			const definitionCount = await prisma.definition.count();
			
			console.log(`📝 Total words: ${wordCount.toLocaleString()}`);
			console.log(`🔗 Words with WordNet data: ${wordNetCount.toLocaleString()}`);
			console.log(`📖 Total definitions: ${definitionCount.toLocaleString()}`);
			console.log(`📊 WordNet coverage: ${((wordNetCount / wordCount) * 100).toFixed(1)}%`);
			
			await prisma.$disconnect();
		} catch (error) {
			console.log('⚠️  Could not retrieve database statistics');
		}
	}

	async quickStart(): Promise<void> {
		console.log('🚀 WordNet Quick Start Setup...\n');
		
		await this.setup({
			pos: 'noun',
			maxWords: 1000,
			batchSize: 50,
			cleanup: true
		});
		
		console.log('\n✅ Quick start completed!');
		console.log('📚 Loaded 1000 most common nouns with WordNet data');
		console.log('💡 Run with --pos=verb --max-words=500 to add verbs');
	}

	async fullSetup(): Promise<void> {
		console.log('🚀 WordNet Full Setup (This may take 2-4 hours)...\n');
		
		const startTime = Date.now();
		
		// Load all parts of speech
		const posOrder = ['noun', 'verb', 'adj', 'adv'];
		
		for (const pos of posOrder) {
			console.log(`\n📚 Loading ${pos}s...`);
			const posStartTime = Date.now();
			
			await this.setup({
				skipDownload: true, // Only download once
				pos,
				batchSize: 200, // Larger batches for better performance
				cleanup: false
			});
			
			const posTime = (Date.now() - posStartTime) / 1000 / 60;
			console.log(`✅ ${pos}s completed in ${posTime.toFixed(1)} minutes`);
		}
		
		const totalTime = (Date.now() - startTime) / 1000 / 60;
		console.log(`\n🎉 Full setup completed in ${totalTime.toFixed(1)} minutes!`);
	}
}

// CLI interface
async function main() {
	const args = process.argv.slice(2);
	
	const options: SetupOptions = {
		forceDownload: args.includes('--force-download'),
		skipDownload: args.includes('--skip-download'),
		pos: args.find(arg => arg.startsWith('--pos='))?.split('=')[1],
		maxWords: parseInt(args.find(arg => arg.startsWith('--max-words='))?.split('=')[1] || '0') || undefined,
		batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1] || '100'),
		dryRun: args.includes('--dry-run'),
		forceLoad: args.includes('--force-load'),
		stats: args.includes('--stats'),
		verbose: args.includes('--verbose'),
		cleanup: !args.includes('--no-cleanup')
	};
	
	if (args.includes('--help') || args.includes('-h')) {
		console.log(`
WordNet Complete Setup Script

Usage: tsx scripts/setup-wordnet.ts [command] [options]

Commands:
  (default)     Complete setup with specified options
  quick-start   Quick setup with 1000 common nouns
  full-setup    Complete setup with all WordNet data (~155k words)

Options:
  --force-download      Force re-download even if files exist
  --skip-download       Skip download step (use existing files)
  --pos=<pos>          Load specific part of speech (noun, verb, adj, adv)
  --max-words=<count>  Maximum words to process
  --batch-size=<size>  Batch size for processing (default: 100)
  --dry-run           Run without saving to database
  --force-load        Force reload even if data exists
  --stats             Show current statistics only
  --verbose           Verbose output
  --no-cleanup        Keep downloaded archives
  --help, -h          Show this help message

Examples:
  # Quick start (recommended for testing)
  tsx scripts/setup-wordnet.ts quick-start
  
  # Load specific part of speech
  tsx scripts/setup-wordnet.ts --pos=noun --max-words=5000
  
  # Full setup (production)
  tsx scripts/setup-wordnet.ts full-setup
  
  # Show current statistics
  tsx scripts/setup-wordnet.ts --stats
  
  # Test run without saving
  tsx scripts/setup-wordnet.ts --pos=noun --max-words=100 --dry-run
		`);
		return;
	}
	
	const setup = new WordNetSetup();
	
	// Handle special commands
	if (args.includes('quick-start')) {
		await setup.quickStart();
	} else if (args.includes('full-setup')) {
		await setup.fullSetup();
	} else {
		await setup.setup(options);
	}
}

if (require.main === module) {
	main().catch(console.error);
}

export { WordNetSetup };
