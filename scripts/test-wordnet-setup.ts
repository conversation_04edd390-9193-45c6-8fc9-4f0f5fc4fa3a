#!/usr/bin/env tsx

/**
 * WordNet Setup Test Script
 * 
 * This script tests the complete WordNet setup and integration to ensure
 * everything is working correctly. It performs comprehensive checks on
 * downloads, database loading, and API functionality.
 * 
 * Usage:
 *   yarn test-wordnet-setup
 *   tsx scripts/test-wordnet-setup.ts [options]
 * 
 * Features:
 * - Tests file downloads and extraction
 * - Verifies database integration
 * - Tests API functionality
 * - Provides detailed reporting
 * - Suggests fixes for issues
 */

import fs from 'fs/promises';
import path from 'path';
import { PrismaClient, Language } from '@prisma/client';

interface TestResult {
	name: string;
	passed: boolean;
	message: string;
	details?: string;
	suggestion?: string;
}

interface TestOptions {
	verbose?: boolean;
	skipDownload?: boolean;
	skipDatabase?: boolean;
	skipApi?: boolean;
}

class WordNetSetupTester {
	private prisma: PrismaClient;
	private results: TestResult[] = [];
	private wordnetDir = path.join(process.cwd(), 'data', 'wordnet', 'extracted');

	constructor() {
		this.prisma = new PrismaClient();
	}

	async runTests(options: TestOptions = {}): Promise<void> {
		console.log('🧪 WordNet Setup Test Suite\n');
		
		try {
			// Test 1: Dependencies
			await this.testDependencies();
			
			// Test 2: File Downloads (if not skipped)
			if (!options.skipDownload) {
				await this.testFileDownloads();
			}
			
			// Test 3: Database Integration (if not skipped)
			if (!options.skipDatabase) {
				await this.testDatabaseIntegration();
			}
			
			// Test 4: API Functionality (if not skipped)
			if (!options.skipApi) {
				await this.testApiFunctionality();
			}
			
			// Test 5: Performance
			await this.testPerformance();
			
			// Show results
			this.showResults(options.verbose);
			
		} catch (error) {
			console.error('❌ Test suite failed:', error);
			throw error;
		} finally {
			await this.prisma.$disconnect();
		}
	}

	private async testDependencies(): Promise<void> {
		console.log('📦 Testing dependencies...');
		
		const dependencies = [
			{ name: 'unzipper', required: true },
			{ name: 'natural', required: true },
			{ name: 'wordnet-db', required: false },
			{ name: '@prisma/client', required: true }
		];
		
		for (const dep of dependencies) {
			try {
				require.resolve(dep.name);
				this.addResult({
					name: `Dependency: ${dep.name}`,
					passed: true,
					message: 'Available'
				});
			} catch (error) {
				this.addResult({
					name: `Dependency: ${dep.name}`,
					passed: !dep.required,
					message: dep.required ? 'Missing (required)' : 'Missing (optional)',
					suggestion: dep.required ? `Run: yarn add ${dep.name}` : undefined
				});
			}
		}
	}

	private async testFileDownloads(): Promise<void> {
		console.log('📥 Testing file downloads...');
		
		// Test directory structure
		try {
			await fs.access(this.wordnetDir);
			this.addResult({
				name: 'WordNet directory',
				passed: true,
				message: 'Exists'
			});
		} catch {
			this.addResult({
				name: 'WordNet directory',
				passed: false,
				message: 'Not found',
				suggestion: 'Run: yarn download-wordnet'
			});
			return;
		}
		
		// Test required files
		const requiredFiles = [
			'data.noun', 'data.verb', 'data.adj', 'data.adv',
			'index.noun', 'index.verb', 'index.adj', 'index.adv'
		];
		
		for (const file of requiredFiles) {
			try {
				const filePath = path.join(this.wordnetDir, file);
				const stat = await fs.stat(filePath);
				const sizeMB = (stat.size / 1024 / 1024).toFixed(2);
				
				this.addResult({
					name: `WordNet file: ${file}`,
					passed: true,
					message: `Available (${sizeMB} MB)`
				});
			} catch {
				this.addResult({
					name: `WordNet file: ${file}`,
					passed: false,
					message: 'Missing',
					suggestion: 'Run: yarn download-wordnet --force'
				});
			}
		}
	}

	private async testDatabaseIntegration(): Promise<void> {
		console.log('🗄️  Testing database integration...');
		
		// Test database connection
		try {
			await this.prisma.$connect();
			this.addResult({
				name: 'Database connection',
				passed: true,
				message: 'Connected'
			});
		} catch (error) {
			this.addResult({
				name: 'Database connection',
				passed: false,
				message: 'Failed to connect',
				details: error instanceof Error ? error.message : 'Unknown error',
				suggestion: 'Check DATABASE_URL and ensure PostgreSQL is running'
			});
			return;
		}
		
		// Test WordNet table exists
		try {
			await this.prisma.wordNetData.findFirst();
			this.addResult({
				name: 'WordNetData table',
				passed: true,
				message: 'Exists'
			});
		} catch (error) {
			this.addResult({
				name: 'WordNetData table',
				passed: false,
				message: 'Missing or inaccessible',
				suggestion: 'Run: yarn p:m'
			});
		}
		
		// Test WordNet data availability
		try {
			const wordNetCount = await this.prisma.wordNetData.count();
			const wordCount = await this.prisma.word.count({
				where: { wordnet_data: { isNot: null } }
			});
			
			if (wordNetCount > 0) {
				this.addResult({
					name: 'WordNet data',
					passed: true,
					message: `${wordNetCount.toLocaleString()} entries, ${wordCount.toLocaleString()} words`,
					details: `Coverage: ${((wordCount / Math.max(wordNetCount, 1)) * 100).toFixed(1)}%`
				});
			} else {
				this.addResult({
					name: 'WordNet data',
					passed: false,
					message: 'No data found',
					suggestion: 'Run: yarn setup-wordnet:quick'
				});
			}
		} catch (error) {
			this.addResult({
				name: 'WordNet data query',
				passed: false,
				message: 'Query failed',
				details: error instanceof Error ? error.message : 'Unknown error'
			});
		}
	}

	private async testApiFunctionality(): Promise<void> {
		console.log('🔌 Testing API functionality...');
		
		// Test WordNet service
		try {
			const { getWordNetService } = await import('../src/backend/wire');
			const wordNetService = getWordNetService();
			
			this.addResult({
				name: 'WordNet service',
				passed: true,
				message: 'Service available'
			});
			
			// Test WordNet lookup
			const testWord = 'house';
			const wordNetInfo = await wordNetService.getWordNetInfo(testWord, Language.EN);
			
			if (wordNetInfo && (wordNetInfo.synsets.length > 0 || wordNetInfo.lemma)) {
				this.addResult({
					name: 'WordNet lookup',
					passed: true,
					message: `Found data for "${testWord}"`,
					details: `Synsets: ${wordNetInfo.synsets.length}, Hypernyms: ${wordNetInfo.hypernyms.length}`
				});
			} else {
				this.addResult({
					name: 'WordNet lookup',
					passed: false,
					message: `No data found for "${testWord}"`,
					suggestion: 'Load more WordNet data or check if word exists in database'
				});
			}
		} catch (error) {
			this.addResult({
				name: 'WordNet service',
				passed: false,
				message: 'Service unavailable',
				details: error instanceof Error ? error.message : 'Unknown error',
				suggestion: 'Check service implementation and dependencies'
			});
		}
		
		// Test word repository
		try {
			const { getWordRepository } = await import('../src/backend/wire');
			const wordRepository = getWordRepository();
			
			const wordsWithWordNet = await wordRepository.searchWordsWithWordNet('test', Language.EN, 5);
			
			this.addResult({
				name: 'Word repository WordNet search',
				passed: true,
				message: `Found ${wordsWithWordNet.length} words with WordNet data`
			});
		} catch (error) {
			this.addResult({
				name: 'Word repository',
				passed: false,
				message: 'Repository test failed',
				details: error instanceof Error ? error.message : 'Unknown error'
			});
		}
	}

	private async testPerformance(): Promise<void> {
		console.log('⚡ Testing performance...');
		
		try {
			// Test database query performance
			const startTime = Date.now();
			const sampleWords = await this.prisma.word.findMany({
				where: { wordnet_data: { isNot: null } },
				include: { wordnet_data: true },
				take: 10
			});
			const queryTime = Date.now() - startTime;
			
			this.addResult({
				name: 'Database query performance',
				passed: queryTime < 1000,
				message: `${queryTime}ms for 10 words with WordNet data`,
				details: queryTime > 1000 ? 'Consider database optimization' : 'Good performance'
			});
			
			// Test WordNet service performance
			if (sampleWords.length > 0) {
				const { getWordNetService } = await import('../src/backend/wire');
				const wordNetService = getWordNetService();
				
				const serviceStartTime = Date.now();
				await wordNetService.getWordNetInfo(sampleWords[0].term, Language.EN);
				const serviceTime = Date.now() - serviceStartTime;
				
				this.addResult({
					name: 'WordNet service performance',
					passed: serviceTime < 500,
					message: `${serviceTime}ms for WordNet lookup`,
					details: serviceTime > 500 ? 'Consider caching optimization' : 'Good performance'
				});
			}
		} catch (error) {
			this.addResult({
				name: 'Performance test',
				passed: false,
				message: 'Performance test failed',
				details: error instanceof Error ? error.message : 'Unknown error'
			});
		}
	}

	private addResult(result: TestResult): void {
		this.results.push(result);
		const status = result.passed ? '✅' : '❌';
		console.log(`  ${status} ${result.name}: ${result.message}`);
		
		if (result.details) {
			console.log(`     ${result.details}`);
		}
		
		if (result.suggestion) {
			console.log(`     💡 ${result.suggestion}`);
		}
	}

	private showResults(verbose?: boolean): void {
		const passed = this.results.filter(r => r.passed).length;
		const total = this.results.length;
		const failed = total - passed;
		
		console.log('\n📊 Test Results Summary\n');
		console.log(`✅ Passed: ${passed}/${total}`);
		console.log(`❌ Failed: ${failed}/${total}`);
		console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%\n`);
		
		if (failed > 0) {
			console.log('❌ Failed Tests:');
			this.results
				.filter(r => !r.passed)
				.forEach(r => {
					console.log(`   • ${r.name}: ${r.message}`);
					if (r.suggestion) {
						console.log(`     💡 ${r.suggestion}`);
					}
				});
			console.log('');
		}
		
		if (verbose) {
			console.log('📋 Detailed Results:');
			this.results.forEach(r => {
				const status = r.passed ? '✅' : '❌';
				console.log(`   ${status} ${r.name}: ${r.message}`);
				if (r.details) {
					console.log(`      ${r.details}`);
				}
			});
		}
		
		// Recommendations
		console.log('💡 Recommendations:\n');
		
		if (failed === 0) {
			console.log('🎉 All tests passed! Your WordNet setup is working correctly.');
			console.log('');
			console.log('Next steps:');
			console.log('• Use WordNet data in your application');
			console.log('• Monitor performance with real usage');
			console.log('• Consider loading more data if needed');
		} else {
			console.log('🔧 Fix the failed tests above to ensure proper WordNet functionality.');
			console.log('');
			console.log('Common solutions:');
			console.log('• Install missing dependencies: yarn install-wordnet-deps');
			console.log('• Download WordNet files: yarn download-wordnet');
			console.log('• Load WordNet data: yarn setup-wordnet:quick');
			console.log('• Check database connection: yarn p:m');
		}
	}
}

// CLI interface
async function main() {
	const args = process.argv.slice(2);
	
	const options: TestOptions = {
		verbose: args.includes('--verbose') || args.includes('-v'),
		skipDownload: args.includes('--skip-download'),
		skipDatabase: args.includes('--skip-database'),
		skipApi: args.includes('--skip-api')
	};
	
	if (args.includes('--help') || args.includes('-h')) {
		console.log(`
WordNet Setup Test Script

Usage: tsx scripts/test-wordnet-setup.ts [options]

Options:
  --verbose, -v      Show detailed test results
  --skip-download    Skip file download tests
  --skip-database    Skip database integration tests
  --skip-api         Skip API functionality tests
  --help, -h         Show this help message

This script tests:
  1. Dependencies installation
  2. WordNet file downloads and extraction
  3. Database integration and data availability
  4. API functionality and service integration
  5. Performance characteristics

Examples:
  tsx scripts/test-wordnet-setup.ts
  tsx scripts/test-wordnet-setup.ts --verbose
  tsx scripts/test-wordnet-setup.ts --skip-download
		`);
		return;
	}
	
	const tester = new WordNetSetupTester();
	await tester.runTests(options);
}

if (require.main === module) {
	main().catch(console.error);
}

export { WordNetSetupTester };
