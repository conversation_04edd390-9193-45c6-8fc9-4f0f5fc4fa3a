#!/usr/bin/env tsx

/**
 * WordNet Download and Setup Script
 * 
 * This script downloads WordNet 3.0 database files from Princeton University
 * and sets them up for use with the vocab application.
 * 
 * Usage:
 *   yarn download-wordnet
 *   tsx scripts/download-wordnet.ts
 * 
 * Features:
 * - Downloads WordNet 3.0 database files
 * - Extracts and organizes files
 * - Verifies file integrity
 * - Sets up directory structure
 */

import fs from 'fs/promises';
import path from 'path';
import https from 'https';
import { createWriteStream, createReadStream } from 'fs';
import { pipeline } from 'stream/promises';
import { createGunzip } from 'zlib';
import { Extract } from 'unzipper';

interface DownloadOptions {
	force?: boolean;
	verify?: boolean;
	cleanup?: boolean;
}

class WordNetDownloader {
	private readonly baseDir = path.join(process.cwd(), 'data', 'wordnet');
	private readonly downloadDir = path.join(this.baseDir, 'downloads');
	private readonly extractDir = path.join(this.baseDir, 'extracted');
	
	// WordNet 3.0 download URLs
	private readonly downloadUrls = {
		// Primary source: Princeton University
		wordnet30: 'http://wordnetcode.princeton.edu/3.0/WordNet-3.0.tar.gz',
		// Alternative: wordnet-db npm package data
		wordnetDb: 'https://github.com/moos/wordnet-db/raw/master/wordnet-db-3.1.tar.gz'
	};

	private readonly expectedFiles = [
		'data.noun', 'data.verb', 'data.adj', 'data.adv',
		'index.noun', 'index.verb', 'index.adj', 'index.adv',
		'noun.exc', 'verb.exc', 'adj.exc', 'adv.exc'
	];

	async download(options: DownloadOptions = {}): Promise<void> {
		console.log('🚀 Starting WordNet download and setup...');
		
		try {
			// Create directories
			await this.createDirectories();
			
			// Check if already downloaded
			if (!options.force && await this.isAlreadyDownloaded()) {
				console.log('✅ WordNet files already exist. Use --force to re-download.');
				return;
			}
			
			// Download WordNet files
			await this.downloadWordNetFiles();
			
			// Extract files
			await this.extractFiles();
			
			// Verify files
			if (options.verify !== false) {
				await this.verifyFiles();
			}
			
			// Cleanup downloads
			if (options.cleanup !== false) {
				await this.cleanupDownloads();
			}
			
			console.log('✅ WordNet download and setup completed successfully!');
			console.log(`📁 WordNet files are available at: ${this.extractDir}`);
			
		} catch (error) {
			console.error('❌ Error during WordNet setup:', error);
			throw error;
		}
	}

	private async createDirectories(): Promise<void> {
		console.log('📁 Creating directories...');
		await fs.mkdir(this.baseDir, { recursive: true });
		await fs.mkdir(this.downloadDir, { recursive: true });
		await fs.mkdir(this.extractDir, { recursive: true });
	}

	private async isAlreadyDownloaded(): Promise<boolean> {
		try {
			const files = await fs.readdir(this.extractDir);
			const hasRequiredFiles = this.expectedFiles.every(file => 
				files.includes(file)
			);
			return hasRequiredFiles;
		} catch {
			return false;
		}
	}

	private async downloadWordNetFiles(): Promise<void> {
		console.log('⬇️  Downloading WordNet files...');
		
		// Try primary source first
		const primaryFile = path.join(this.downloadDir, 'WordNet-3.0.tar.gz');
		
		try {
			await this.downloadFile(this.downloadUrls.wordnet30, primaryFile);
			console.log('✅ Downloaded from Princeton University');
		} catch (error) {
			console.log('⚠️  Primary download failed, trying alternative source...');
			
			// Try alternative source
			const altFile = path.join(this.downloadDir, 'wordnet-db-3.1.tar.gz');
			await this.downloadFile(this.downloadUrls.wordnetDb, altFile);
			console.log('✅ Downloaded from alternative source');
		}
	}

	private async downloadFile(url: string, filePath: string): Promise<void> {
		return new Promise((resolve, reject) => {
			const file = createWriteStream(filePath);
			
			https.get(url, (response) => {
				if (response.statusCode !== 200) {
					reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
					return;
				}
				
				const totalSize = parseInt(response.headers['content-length'] || '0');
				let downloadedSize = 0;
				
				response.on('data', (chunk) => {
					downloadedSize += chunk.length;
					if (totalSize > 0) {
						const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
						process.stdout.write(`\r📥 Progress: ${progress}% (${downloadedSize}/${totalSize} bytes)`);
					}
				});
				
				response.pipe(file);
				
				file.on('finish', () => {
					file.close();
					console.log('\n✅ Download completed');
					resolve();
				});
				
				file.on('error', (err) => {
					fs.unlink(filePath).catch(() => {});
					reject(err);
				});
			}).on('error', reject);
		});
	}

	private async extractFiles(): Promise<void> {
		console.log('📦 Extracting files...');
		
		const downloadedFiles = await fs.readdir(this.downloadDir);
		const tarGzFile = downloadedFiles.find(file => file.endsWith('.tar.gz'));
		
		if (!tarGzFile) {
			throw new Error('No tar.gz file found in downloads');
		}
		
		const filePath = path.join(this.downloadDir, tarGzFile);
		
		// Extract tar.gz file
		await this.extractTarGz(filePath, this.extractDir);
		
		// Move WordNet dict files to root of extract directory
		await this.organizeExtractedFiles();
	}

	private async extractTarGz(filePath: string, extractPath: string): Promise<void> {
		const readStream = createReadStream(filePath);
		const gunzip = createGunzip();
		
		await pipeline(
			readStream,
			gunzip,
			Extract({ path: extractPath })
		);
	}

	private async organizeExtractedFiles(): Promise<void> {
		console.log('📋 Organizing extracted files...');
		
		// Look for dict directory in extracted files
		const extractedItems = await fs.readdir(this.extractDir);
		
		for (const item of extractedItems) {
			const itemPath = path.join(this.extractDir, item);
			const stat = await fs.stat(itemPath);
			
			if (stat.isDirectory()) {
				// Look for dict subdirectory
				const dictPath = path.join(itemPath, 'dict');
				try {
					const dictStat = await fs.stat(dictPath);
					if (dictStat.isDirectory()) {
						// Move files from dict to root extract directory
						const dictFiles = await fs.readdir(dictPath);
						for (const file of dictFiles) {
							const srcPath = path.join(dictPath, file);
							const destPath = path.join(this.extractDir, file);
							await fs.rename(srcPath, destPath);
						}
						console.log(`✅ Moved files from ${dictPath} to ${this.extractDir}`);
						break;
					}
				} catch {
					// dict directory doesn't exist, continue
				}
			}
		}
	}

	private async verifyFiles(): Promise<void> {
		console.log('🔍 Verifying files...');
		
		const files = await fs.readdir(this.extractDir);
		const missingFiles = this.expectedFiles.filter(file => !files.includes(file));
		
		if (missingFiles.length > 0) {
			console.warn('⚠️  Missing files:', missingFiles);
		} else {
			console.log('✅ All expected files are present');
		}
		
		// Check file sizes
		for (const file of this.expectedFiles) {
			const filePath = path.join(this.extractDir, file);
			try {
				const stat = await fs.stat(filePath);
				console.log(`📄 ${file}: ${(stat.size / 1024 / 1024).toFixed(2)} MB`);
			} catch {
				console.warn(`⚠️  Could not stat file: ${file}`);
			}
		}
	}

	private async cleanupDownloads(): Promise<void> {
		console.log('🧹 Cleaning up downloads...');
		try {
			await fs.rm(this.downloadDir, { recursive: true, force: true });
			console.log('✅ Download files cleaned up');
		} catch (error) {
			console.warn('⚠️  Could not cleanup downloads:', error);
		}
	}

	getExtractDir(): string {
		return this.extractDir;
	}
}

// CLI interface
async function main() {
	const args = process.argv.slice(2);
	const options: DownloadOptions = {
		force: args.includes('--force'),
		verify: !args.includes('--no-verify'),
		cleanup: !args.includes('--no-cleanup')
	};
	
	if (args.includes('--help') || args.includes('-h')) {
		console.log(`
WordNet Download Script

Usage: tsx scripts/download-wordnet.ts [options]

Options:
  --force       Force re-download even if files exist
  --no-verify   Skip file verification
  --no-cleanup  Keep downloaded archives
  --help, -h    Show this help message

Examples:
  tsx scripts/download-wordnet.ts
  tsx scripts/download-wordnet.ts --force
  tsx scripts/download-wordnet.ts --no-cleanup
		`);
		return;
	}
	
	const downloader = new WordNetDownloader();
	await downloader.download(options);
}

if (require.main === module) {
	main().catch(console.error);
}

export { WordNetDownloader };
