'use client';

import { LoadingSpinner } from '@/components/ui';
import { useAuth } from '@/contexts/auth-context';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

interface AuthGuardProps {
	children: React.ReactNode;
	fallbackPath?: string;
}

export function AuthGuard({ children, fallbackPath = '/login' }: AuthGuardProps) {
	const { user, isLoading, getUser } = useAuth();
	const router = useRouter();
	const [isInitialized, setIsInitialized] = useState(false);
	const hasAttemptedAuth = useRef(false);

	useEffect(() => {
		const initAuth = async () => {
			// Only attempt auth once and when not already loading
			if (!hasAttemptedAuth.current && !user && !isLoading) {
				hasAttemptedAuth.current = true;
				await getUser();
			}
			setIsInitialized(true);
		};

		initAuth();
	}, [user, isLoading, getUser]);

	useEffect(() => {
		if (isInitialized && !isLoading && !user) {
			router.push(fallbackPath);
		}
	}, [isInitialized, isLoading, user, router, fallbackPath]);

	if (!isInitialized || isLoading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<LoadingSpinner size="lg" />
			</div>
		);
	}

	if (!user) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
				<div className="text-center max-w-md mx-auto p-8">
					<div className="text-blue-500 text-6xl mb-6">🔐</div>
					<h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
						Authentication Required
					</h1>
					<p className="text-gray-600 dark:text-gray-400 mb-6">
						Please sign in to access this page. You will be redirected to the login page
						shortly.
					</p>
					<div className="flex items-center justify-center space-x-2 text-sm text-gray-500 dark:text-gray-500">
						<LoadingSpinner size="sm" />
						<span>Redirecting...</span>
					</div>
				</div>
			</div>
		);
	}

	return <>{children}</>;
}
