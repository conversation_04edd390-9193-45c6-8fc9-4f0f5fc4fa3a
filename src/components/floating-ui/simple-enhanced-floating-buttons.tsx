'use client';

import React, { useState, useContext } from 'react';
import { Button } from '@/components/ui';
import { Settings, MessageSquare, HelpCircle, Loader2 } from 'lucide-react';
import { useTranslation, useGuidance } from '@/contexts';
import { LoadingContext } from '@/contexts/loading-context';
import { EnhancedSettingsPanel } from '@/components/settings/enhanced-settings-panel';
import { EnhancedFeedbackForm } from '@/components/feedback/enhanced-feedback-form';
import { GuidancePanel } from '@/components/onboarding';
import { motion, AnimatePresence } from 'framer-motion';
import {
	settingsButtonVariants,
	floatingFeedbackButtonVariants,
	guidanceButtonVariants,
} from '@/components/animations';

interface SimpleEnhancedFloatingButtonsProps {
	includeSettings?: boolean;
	includeFeedback?: boolean;
	includeGuidance?: boolean;
	includeLoading?: boolean;
}

export function SimpleEnhancedFloatingButtons({
	includeSettings = true,
	includeFeedback = true,
	includeGuidance = false,
	includeLoading = true,
}: SimpleEnhancedFloatingButtonsProps) {
	const { t } = useTranslation();
	const {
		isOpen: isGuidanceOpen,
		config: guidanceConfig,
		closeGuidance,
		toggleGuidance,
	} = useGuidance();
	const loadingContext = useContext(LoadingContext);
	if (!loadingContext)
		throw new Error('SimpleEnhancedFloatingButtons must be used within a LoadingProvider');
	const { isLoading, loadingStates } = loadingContext;
	const [isSettingsOpen, setIsSettingsOpen] = useState(false);
	const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);

	// Check if any loading state is active
	const hasAnyLoading = isLoading || Object.values(loadingStates).some((state) => state);

	return (
		<>
			{/* Fixed Floating Buttons */}
			<div className="fixed bottom-6 right-6 z-50 flex flex-col-reverse items-end gap-2">
				{includeSettings && (
					<motion.div
						variants={settingsButtonVariants}
						initial="idle"
						whileHover="hover"
						whileTap="tap"
					>
						<Button
							size="icon"
							className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-200"
							onClick={() => setIsSettingsOpen(true)}
							title={t('settings.button')}
							aria-label={t('settings.button')}
						>
							<Settings className="h-6 w-6" />
						</Button>
					</motion.div>
				)}
				{includeFeedback && (
					<motion.div
						variants={floatingFeedbackButtonVariants}
						initial="idle"
						whileHover="hover"
						whileTap="tap"
					>
						<Button
							size="icon"
							className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-200 bg-primary hover:bg-primary/90"
							onClick={() => setIsFeedbackOpen(true)}
							title={t('feedback.button_title')}
							aria-label={t('feedback.button_title')}
						>
							<MessageSquare className="h-6 w-6" />
						</Button>
					</motion.div>
				)}
				{includeGuidance && guidanceConfig && (
					<motion.div
						variants={guidanceButtonVariants}
						initial="idle"
						whileHover="hover"
						whileTap="tap"
					>
						<Button
							size="icon"
							className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-200 bg-blue-600 hover:bg-blue-700 text-white"
							onClick={toggleGuidance}
							title={t('guidance.button')}
							aria-label={t('guidance.button')}
						>
							<HelpCircle className="h-6 w-6" />
						</Button>
					</motion.div>
				)}

				{/* Global Loading Button - Always on top */}
				{includeLoading && (
					<AnimatePresence>
						{hasAnyLoading && (
							<motion.div
								initial={{ opacity: 0, scale: 0.8 }}
								animate={{ opacity: 1, scale: 1 }}
								exit={{ opacity: 0, scale: 0.8 }}
								transition={{ duration: 0.2 }}
							>
								<Button
									disabled
									size="icon"
									className="h-14 w-14 rounded-full shadow-lg pointer-events-none opacity-70"
								>
									<Loader2 className="h-8 w-8 animate-spin text-background" />
								</Button>
							</motion.div>
						)}
					</AnimatePresence>
				)}
			</div>

			{/* Enhanced Settings Panel */}
			<EnhancedSettingsPanel
				isOpen={isSettingsOpen}
				onClose={() => setIsSettingsOpen(false)}
			/>

			{/* Enhanced Feedback Form */}
			<EnhancedFeedbackForm
				isOpen={isFeedbackOpen}
				onClose={() => setIsFeedbackOpen(false)}
			/>

			{/* Guidance Panel */}
			{guidanceConfig && (
				<GuidancePanel
					isOpen={isGuidanceOpen}
					onClose={closeGuidance}
					titleKey={guidanceConfig.titleKey}
					steps={guidanceConfig.steps}
					tipKey={guidanceConfig.tipKey}
					requirementKey={guidanceConfig.requirementKey}
				/>
			)}
		</>
	);
}
