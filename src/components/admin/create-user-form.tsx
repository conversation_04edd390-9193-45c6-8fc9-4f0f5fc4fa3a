'use client';

import { useState } from 'react';
import {
	<PERSON><PERSON>,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	Label,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	LoadingSpinner,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { Provider, Role } from '@prisma/client';
import { X, UserPlus } from 'lucide-react';

interface CreateUserFormProps {
	onSuccess: () => void;
	onCancel: () => void;
}

export function CreateUserForm({ onSuccess, onCancel }: CreateUserFormProps) {
	const [formData, setFormData] = useState<{
		username: string;
		password: string;
		confirmPassword: string;
		email: string;
		name: string;
		provider: Provider;
		role: Role;
	}>({
		username: '',
		password: '',
		confirmPassword: '',
		email: '',
		name: '',
		provider: Provider.USERNAME_PASSWORD,
		role: Role.USER,
	});
	const [loading, setLoading] = useState(false);
	const { showSuccess, showError } = useToast();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (formData.provider === Provider.USERNAME_PASSWORD) {
			if (!formData.username.trim()) {
				showError(new Error('Username is required'));
				return;
			}
			if (!formData.password) {
				showError(new Error('Password is required'));
				return;
			}
			if (formData.password !== formData.confirmPassword) {
				showError(new Error('Passwords do not match'));
				return;
			}
			if (formData.password.length < 6) {
				showError(new Error('Password must be at least 6 characters long'));
				return;
			}
		}

		setLoading(true);

		try {
			const response = await fetch('/api/admin/users', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					username: formData.username.trim() || undefined,
					password: formData.password || undefined,
					email: formData.email.trim() || undefined,
					name: formData.name.trim() || undefined,
					provider: formData.provider,
					role: formData.role,
				}),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				showSuccess('User created successfully');
				onSuccess();
			} else {
				throw new Error(data.error || 'Failed to create user');
			}
		} catch (error) {
			showError(new Error('Failed to create user'));
		} finally {
			setLoading(false);
		}
	};

	return (
		<Card className="w-full max-w-md mx-auto">
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center">
						<UserPlus className="h-5 w-5 mr-2" />
						Create New User
					</CardTitle>
					<Button variant="ghost" size="sm" onClick={onCancel}>
						<X className="h-4 w-4" />
					</Button>
				</div>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					{/* Provider Selection */}
					<div className="space-y-2">
						<Label htmlFor="provider">Provider</Label>
						<Select
							value={formData.provider}
							onValueChange={(value: Provider) =>
								setFormData({ ...formData, provider: value })
							}
						>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value={Provider.USERNAME_PASSWORD}>
									Username/Password
								</SelectItem>
								<SelectItem value={Provider.TELEGRAM}>Telegram</SelectItem>
								<SelectItem value={Provider.GOOGLE}>Google</SelectItem>
							</SelectContent>
						</Select>
					</div>

					{/* Username (for USERNAME_PASSWORD provider) */}
					{formData.provider === Provider.USERNAME_PASSWORD && (
						<div className="space-y-2">
							<Label htmlFor="username">Username *</Label>
							<Input
								id="username"
								type="text"
								value={formData.username}
								onChange={(e) =>
									setFormData({ ...formData, username: e.target.value })
								}
								placeholder="Enter username"
								required
							/>
						</div>
					)}

					{/* Password (for USERNAME_PASSWORD provider) */}
					{formData.provider === Provider.USERNAME_PASSWORD && (
						<>
							<div className="space-y-2">
								<Label htmlFor="password">Password *</Label>
								<Input
									id="password"
									type="password"
									value={formData.password}
									onChange={(e) =>
										setFormData({ ...formData, password: e.target.value })
									}
									placeholder="Enter password"
									required
								/>
							</div>

							<div className="space-y-2">
								<Label htmlFor="confirmPassword">Confirm Password *</Label>
								<Input
									id="confirmPassword"
									type="password"
									value={formData.confirmPassword}
									onChange={(e) =>
										setFormData({
											...formData,
											confirmPassword: e.target.value,
										})
									}
									placeholder="Confirm password"
									required
								/>
							</div>
						</>
					)}

					{/* Email */}
					<div className="space-y-2">
						<Label htmlFor="email">Email</Label>
						<Input
							id="email"
							type="email"
							value={formData.email}
							onChange={(e) => setFormData({ ...formData, email: e.target.value })}
							placeholder="Enter email"
						/>
					</div>

					{/* Name */}
					<div className="space-y-2">
						<Label htmlFor="name">Name</Label>
						<Input
							id="name"
							type="text"
							value={formData.name}
							onChange={(e) => setFormData({ ...formData, name: e.target.value })}
							placeholder="Enter full name"
						/>
					</div>

					{/* Role */}
					<div className="space-y-2">
						<Label htmlFor="role">Role</Label>
						<Select
							value={formData.role}
							onValueChange={(value: Role) =>
								setFormData({ ...formData, role: value })
							}
						>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value={Role.USER}>User</SelectItem>
								<SelectItem value={Role.ADMIN}>Admin</SelectItem>
							</SelectContent>
						</Select>
					</div>

					{/* Submit Buttons */}
					<div className="flex space-x-2 pt-4">
						<Button type="submit" disabled={loading} className="flex-1">
							{loading ? (
								<>
									<LoadingSpinner className="h-4 w-4 mr-2" />
									Creating...
								</>
							) : (
								'Create User'
							)}
						</Button>
						<Button type="button" variant="outline" onClick={onCancel}>
							Cancel
						</Button>
					</div>
				</form>
			</CardContent>
		</Card>
	);
}
