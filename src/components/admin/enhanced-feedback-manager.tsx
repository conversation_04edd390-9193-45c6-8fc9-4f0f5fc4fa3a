'use client';

import { useState, useEffect, useCallback } from 'react';
import {
	<PERSON><PERSON>,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	LoadingSpinner,
	Badge,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	Filter,
	RefreshCw,
	Search,
	Trash2,
	CheckSquare,
	Square,
	MoreHorizontal,
	Eye,
	AlertTriangle,
} from 'lucide-react';

interface User {
	id: string;
	username: string | null;
	provider: string;
	provider_id: string;
	created_at: string;
}

interface Feedback {
	id: string;
	message: string;
	user_id: string;
	user: User;
	status: string;
	created_at: string;
}

interface FeedbackResponse {
	feedbacks: Feedback[];
	total: number;
	page: number;
	limit: number;
}

export function EnhancedFeedbackManager() {
	const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
	const [loading, setLoading] = useState(false);
	const [searchTerm, setSearchTerm] = useState('');
	const [statusFilter, setStatusFilter] = useState('all');
	const [selectedFeedbacks, setSelectedFeedbacks] = useState<Set<string>>(new Set());
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [totalCount, setTotalCount] = useState(0);
	const [bulkDeleting, setBulkDeleting] = useState(false);
	const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
	const { showSuccess, showError } = useToast();

	const limit = 20;

	const loadFeedbacks = useCallback(
		async (page = 1, search = '', status = 'all') => {
			setLoading(true);
			try {
				const params = new URLSearchParams({
					page: page.toString(),
					limit: limit.toString(),
				});

				if (search) params.append('search', search);
				if (status !== 'all') params.append('status', status);

				const response = await fetch(`/api/admin/feedback?${params}`, {
					credentials: 'include',
				});

				if (response.ok) {
					const data = await response.json();
					if (data.success) {
						const result: FeedbackResponse = data.data;
						setFeedbacks(result.feedbacks);
						setTotalCount(result.total);
						setTotalPages(Math.ceil(result.total / limit));
						setCurrentPage(result.page);
					} else {
						throw new Error(data.error || 'Failed to load feedback');
					}
				} else {
					throw new Error('Failed to load feedback');
				}
			} catch (error) {
				showError(new Error('Failed to load feedback'));
			} finally {
				setLoading(false);
			}
		},
		[showError, limit]
	);

	useEffect(() => {
		const loadInitialFeedbacks = async () => {
			setLoading(true);
			try {
				const params = new URLSearchParams({
					page: '1',
					limit: limit.toString(),
				});

				const response = await fetch(`/api/admin/feedback?${params}`, {
					credentials: 'include',
				});

				if (response.ok) {
					const data = await response.json();
					console.log('Feedback data:', data);

					if (data.success) {
						const result = data.data;
						setFeedbacks(result.feedbacks);
						setTotalCount(result.total);
						setTotalPages(Math.ceil(result.total / limit));
						setCurrentPage(result.page);
					} else {
						throw new Error(data.error || 'Failed to load feedback');
					}
				} else {
					throw new Error('Failed to load feedback');
				}
			} catch (error) {
				console.error('Error loading feedback:', error);
				showError(new Error('Failed to load feedback'));
			} finally {
				setLoading(false);
			}
		};

		loadInitialFeedbacks();
	}, [showError, limit]);

	const handleSearch = () => {
		setCurrentPage(1);
		setSelectedFeedbacks(new Set());
		loadFeedbacks(1, searchTerm, statusFilter);
	};

	const handleRefresh = () => {
		setSelectedFeedbacks(new Set());
		loadFeedbacks(currentPage, searchTerm, statusFilter);
	};

	const handleSelectAll = () => {
		if (selectedFeedbacks.size === feedbacks.length) {
			setSelectedFeedbacks(new Set());
		} else {
			setSelectedFeedbacks(new Set(feedbacks.map((f) => f.id)));
		}
	};

	const handleSelectFeedback = (feedbackId: string) => {
		const newSelected = new Set(selectedFeedbacks);
		if (newSelected.has(feedbackId)) {
			newSelected.delete(feedbackId);
		} else {
			newSelected.add(feedbackId);
		}
		setSelectedFeedbacks(newSelected);
	};

	const updateFeedbackStatus = async (feedbackId: string, status: string) => {
		try {
			const response = await fetch(`/api/admin/feedback/${feedbackId}`, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ status }),
				credentials: 'include',
			});

			if (response.ok) {
				showSuccess('Feedback status updated successfully');
				loadFeedbacks(currentPage, searchTerm, statusFilter);
			} else {
				throw new Error('Failed to update feedback status');
			}
		} catch (error) {
			showError(new Error('Failed to update feedback status'));
		}
	};

	const deleteFeedback = async (feedbackId: string) => {
		setDeletingIds((prev) => new Set(prev).add(feedbackId));
		try {
			const response = await fetch(`/api/admin/feedback/${feedbackId}`, {
				method: 'DELETE',
				credentials: 'include',
			});

			if (response.ok) {
				showSuccess('Feedback deleted successfully');
				setSelectedFeedbacks((prev) => {
					const newSet = new Set(prev);
					newSet.delete(feedbackId);
					return newSet;
				});
				loadFeedbacks(currentPage, searchTerm, statusFilter);
			} else {
				throw new Error('Failed to delete feedback');
			}
		} catch (error) {
			showError(new Error('Failed to delete feedback'));
		} finally {
			setDeletingIds((prev) => {
				const newSet = new Set(prev);
				newSet.delete(feedbackId);
				return newSet;
			});
		}
	};

	const bulkDeleteFeedbacks = async () => {
		if (selectedFeedbacks.size === 0) return;

		setBulkDeleting(true);
		try {
			const response = await fetch('/api/admin/feedback/bulk-delete', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					feedbackIds: Array.from(selectedFeedbacks),
				}),
				credentials: 'include',
			});

			if (response.ok) {
				const data = await response.json();
				if (data.success) {
					showSuccess(data.message);
					setSelectedFeedbacks(new Set());
					loadFeedbacks(currentPage, searchTerm, statusFilter);
				} else {
					throw new Error(data.error || 'Failed to bulk delete feedback');
				}
			} else {
				throw new Error('Failed to bulk delete feedback');
			}
		} catch (error) {
			showError(new Error('Failed to bulk delete feedback'));
		} finally {
			setBulkDeleting(false);
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'pending':
				return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
			case 'reviewed':
				return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
			case 'resolved':
				return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
			case 'dismissed':
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 dark:text-white">
						Feedback Management
					</h1>
					<p className="text-gray-600 dark:text-gray-400 mt-1">
						Manage user feedback and support requests
					</p>
				</div>
				<div className="flex space-x-2">
					<Button onClick={handleRefresh} disabled={loading} variant="outline">
						<RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
						Refresh
					</Button>
				</div>
			</div>

			{/* Stats */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<Card>
					<CardContent className="pt-6">
						<div className="text-2xl font-bold">{totalCount}</div>
						<p className="text-xs text-muted-foreground">Total Feedback</p>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="pt-6">
						<div className="text-2xl font-bold">
							{feedbacks.filter((f) => f.status === 'pending').length}
						</div>
						<p className="text-xs text-muted-foreground">Pending</p>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="pt-6">
						<div className="text-2xl font-bold">
							{feedbacks.filter((f) => f.status === 'resolved').length}
						</div>
						<p className="text-xs text-muted-foreground">Resolved</p>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="pt-6">
						<div className="text-2xl font-bold">{selectedFeedbacks.size}</div>
						<p className="text-xs text-muted-foreground">Selected</p>
					</CardContent>
				</Card>
			</div>

			{/* Filters and Search */}
			<Card>
				<CardContent className="pt-6">
					<div className="flex gap-4 items-center">
						<div className="relative flex-1">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
							<Input
								placeholder="Search feedback or users..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
								className="pl-10"
							/>
						</div>
						<div className="flex items-center gap-2">
							<Filter className="h-4 w-4 text-gray-400 dark:text-gray-500" />
							<Select value={statusFilter} onValueChange={setStatusFilter}>
								<SelectTrigger className="w-40">
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Status</SelectItem>
									<SelectItem value="pending">Pending</SelectItem>
									<SelectItem value="reviewed">Reviewed</SelectItem>
									<SelectItem value="resolved">Resolved</SelectItem>
									<SelectItem value="dismissed">Dismissed</SelectItem>
								</SelectContent>
							</Select>
						</div>
						<Button onClick={handleSearch} variant="outline">
							Search
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Bulk Actions */}
			{selectedFeedbacks.size > 0 && (
				<Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
					<CardContent className="pt-6">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<AlertTriangle className="h-4 w-4 text-orange-600" />
								<span className="text-sm font-medium">
									{selectedFeedbacks.size} feedback(s) selected
								</span>
							</div>
							<div className="flex gap-2">
								<Button
									onClick={bulkDeleteFeedbacks}
									disabled={bulkDeleting}
									variant="destructive"
									size="sm"
								>
									{bulkDeleting ? (
										<>
											<LoadingSpinner className="h-4 w-4 mr-2" />
											Deleting...
										</>
									) : (
										<>
											<Trash2 className="h-4 w-4 mr-2" />
											Delete Selected
										</>
									)}
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Feedback List */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center justify-between">
						<span>Feedback List</span>
						<div className="flex items-center gap-2">
							<Button
								onClick={handleSelectAll}
								variant="outline"
								size="sm"
								className="flex items-center gap-2"
							>
								{selectedFeedbacks.size === feedbacks.length ? (
									<CheckSquare className="h-4 w-4" />
								) : (
									<Square className="h-4 w-4" />
								)}
								Select All
							</Button>
						</div>
					</CardTitle>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div className="flex items-center justify-center h-64">
							<LoadingSpinner className="h-8 w-8" />
						</div>
					) : (
						<div className="space-y-4">
							{feedbacks.map((feedback) => (
								<div
									key={feedback.id}
									className={`border rounded-lg p-4 ${
										selectedFeedbacks.has(feedback.id)
											? 'border-blue-300 bg-blue-50 dark:border-blue-700 dark:bg-blue-950'
											: 'border-gray-200 dark:border-gray-700'
									}`}
								>
									<div className="flex items-start justify-between mb-4">
										<div className="flex items-center gap-3">
											<button
												onClick={() => handleSelectFeedback(feedback.id)}
												className="flex items-center justify-center w-5 h-5 border rounded"
											>
												{selectedFeedbacks.has(feedback.id) ? (
													<CheckSquare className="h-4 w-4 text-blue-600" />
												) : (
													<Square className="h-4 w-4 text-gray-400" />
												)}
											</button>
											<div>
												<div className="flex items-center gap-2 mb-1">
													<Badge
														className={getStatusColor(feedback.status)}
													>
														{feedback.status}
													</Badge>
													<span className="text-sm text-gray-500 dark:text-gray-400">
														{new Date(
															feedback.created_at
														).toLocaleString()}
													</span>
												</div>
												<div className="text-sm text-gray-600 dark:text-gray-400">
													<strong>User:</strong>{' '}
													{feedback.user.username || 'Anonymous'} (
													{feedback.user.provider}:{' '}
													{feedback.user.provider_id})
												</div>
											</div>
										</div>
										<div className="flex items-center gap-2">
											<Select
												value={feedback.status}
												onValueChange={(status) =>
													updateFeedbackStatus(feedback.id, status)
												}
											>
												<SelectTrigger className="w-32">
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="pending">Pending</SelectItem>
													<SelectItem value="reviewed">
														Reviewed
													</SelectItem>
													<SelectItem value="resolved">
														Resolved
													</SelectItem>
													<SelectItem value="dismissed">
														Dismissed
													</SelectItem>
												</SelectContent>
											</Select>
											<Button
												onClick={() => deleteFeedback(feedback.id)}
												disabled={deletingIds.has(feedback.id)}
												variant="destructive"
												size="sm"
											>
												{deletingIds.has(feedback.id) ? (
													<LoadingSpinner className="h-4 w-4" />
												) : (
													<Trash2 className="h-4 w-4" />
												)}
											</Button>
										</div>
									</div>
									<div className="p-4 bg-muted/50 rounded-lg border">
										<p className="text-sm leading-relaxed whitespace-pre-wrap">
											{feedback.message}
										</p>
									</div>
								</div>
							))}
						</div>
					)}

					{/* Pagination */}
					{totalPages > 1 && (
						<div className="flex items-center justify-between mt-6">
							<div className="text-sm text-gray-600 dark:text-gray-400">
								Showing {(currentPage - 1) * limit + 1} to{' '}
								{Math.min(currentPage * limit, totalCount)} of {totalCount} feedback
								items
							</div>
							<div className="flex gap-2">
								<Button
									onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
									disabled={currentPage === 1}
									variant="outline"
									size="sm"
								>
									Previous
								</Button>
								<span className="flex items-center px-3 py-1 text-sm">
									Page {currentPage} of {totalPages}
								</span>
								<Button
									onClick={() =>
										setCurrentPage((prev) => Math.min(totalPages, prev + 1))
									}
									disabled={currentPage === totalPages}
									variant="outline"
									size="sm"
								>
									Next
								</Button>
							</div>
						</div>
					)}

					{feedbacks.length === 0 && !loading && (
						<div className="text-center text-gray-500 dark:text-gray-400 py-8">
							No feedback found matching your criteria.
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
