'use client';

import { useState, useEffect, useCallback } from 'react';
import {
	<PERSON><PERSON>,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	LoadingSpinner,
	Badge,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	Users,
	Search,
	RefreshCw,
	UserPlus,
	Edit,
	Ban,
	CheckCircle,
	Shield,
	User,
	Key,
	Trash2,
	AlertTriangle,
	MoreHorizontal,
} from 'lucide-react';
import { ChangeUserPasswordForm } from './change-user-password-form';
import { CreateUserForm } from './create-user-form';
import { EditUserForm } from './edit-user-form';
import { Role } from '@prisma/client';

interface UserData {
	id: string;
	username: string | null;
	provider: string;
	provider_id: string;
	role: Role;
	disabled: boolean;
	created_at: string;
}

interface UsersResponse {
	users: UserData[];
	total: number;
	page: number;
	limit: number;
}

export function UserManagement() {
	const [users, setUsers] = useState<UserData[]>([]);
	const [loading, setLoading] = useState(true);
	const [refreshing, setRefreshing] = useState(false);
	const [searchTerm, setSearchTerm] = useState('');
	const [currentPage, setCurrentPage] = useState(1);
	const [totalUsers, setTotalUsers] = useState(0);
	const [updatingUsers, setUpdatingUsers] = useState<Set<string>>(new Set());
	const [changePasswordUser, setChangePasswordUser] = useState<UserData | null>(null);
	const [showCreateForm, setShowCreateForm] = useState(false);
	const [showEditForm, setShowEditForm] = useState(false);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
	const { showSuccess, showError } = useToast();

	const limit = 20;

	const fetchUsers = useCallback(
		async (page = 1, search = '', isRefresh = false) => {
			if (isRefresh) {
				setRefreshing(true);
			} else {
				setLoading(true);
			}

			try {
				const params = new URLSearchParams({
					page: page.toString(),
					limit: limit.toString(),
				});

				if (search.trim()) {
					params.append('search', search.trim());
				}

				const response = await fetch(`/api/admin/users?${params}`);

				if (!response.ok) {
					throw new Error('Failed to fetch users');
				}

				const data = await response.json();

				if (data.success) {
					setUsers(data.data.users);
					setTotalUsers(data.data.total);
					setCurrentPage(data.data.page);
				} else {
					throw new Error(data.error || 'Failed to fetch users');
				}
			} catch (error) {
				showError(new Error('Failed to load users'));
			} finally {
				setLoading(false);
				setRefreshing(false);
			}
		},
		[showError]
	);

	useEffect(() => {
		fetchUsers(currentPage, searchTerm);
	}, [currentPage, searchTerm, fetchUsers]);

	const handleSearch = () => {
		setCurrentPage(1);
		fetchUsers(1, searchTerm);
	};

	const handleRefresh = () => {
		fetchUsers(currentPage, searchTerm, true);
	};

	const handleCreateUser = () => {
		setShowCreateForm(true);
	};

	const handleEditUser = (user: UserData) => {
		setSelectedUser(user);
		setShowEditForm(true);
	};

	const handleDeleteUser = (user: UserData) => {
		setSelectedUser(user);
		setShowDeleteDialog(true);
	};

	const confirmDeleteUser = async () => {
		if (!selectedUser) return;

		setUpdatingUsers((prev) => new Set(prev).add(selectedUser.id));

		try {
			const response = await fetch(`/api/admin/users/${selectedUser.id}`, {
				method: 'DELETE',
			});

			const data = await response.json();

			if (response.ok && data.success) {
				showSuccess('User deleted successfully');
				fetchUsers(currentPage, searchTerm);
			} else {
				throw new Error(data.error || 'Failed to delete user');
			}
		} catch (error) {
			showError(new Error('Failed to delete user'));
		} finally {
			setUpdatingUsers((prev) => {
				const newSet = new Set(prev);
				newSet.delete(selectedUser.id);
				return newSet;
			});
			setShowDeleteDialog(false);
			setSelectedUser(null);
		}
	};

	const handleFormSuccess = () => {
		setShowCreateForm(false);
		setShowEditForm(false);
		setSelectedUser(null);
		fetchUsers(currentPage, searchTerm);
	};

	const handleFormCancel = () => {
		setShowCreateForm(false);
		setShowEditForm(false);
		setSelectedUser(null);
	};

	const updateUserRole = async (userId: string, newRole: Role) => {
		setUpdatingUsers((prev) => new Set(prev).add(userId));

		try {
			const response = await fetch('/api/admin/users', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					userId,
					role: newRole,
				}),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				setUsers((prev) =>
					prev.map((user) => (user.id === userId ? { ...user, role: newRole } : user))
				);
				showSuccess(`User role updated to ${newRole}`);
			} else {
				throw new Error(data.error || 'Failed to update user role');
			}
		} catch (error) {
			showError(new Error('Failed to update user role'));
		} finally {
			setUpdatingUsers((prev) => {
				const newSet = new Set(prev);
				newSet.delete(userId);
				return newSet;
			});
		}
	};

	const toggleUserStatus = async (userId: string, currentDisabled: boolean) => {
		setUpdatingUsers((prev) => new Set(prev).add(userId));

		try {
			const response = await fetch('/api/admin/users', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					userId,
					disabled: !currentDisabled,
				}),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				setUsers((prev) =>
					prev.map((user) =>
						user.id === userId ? { ...user, disabled: !currentDisabled } : user
					)
				);
				showSuccess(`User ${!currentDisabled ? 'disabled' : 'enabled'} successfully`);
			} else {
				throw new Error(data.error || 'Failed to update user status');
			}
		} catch (error) {
			showError(new Error('Failed to update user status'));
		} finally {
			setUpdatingUsers((prev) => {
				const newSet = new Set(prev);
				newSet.delete(userId);
				return newSet;
			});
		}
	};

	const getRoleBadgeColor = (role: Role) => {
		switch (role) {
			case Role.ADMIN:
				return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
			case Role.USER:
				return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
		}
	};

	const getStatusBadgeColor = (disabled: boolean) => {
		return disabled
			? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
			: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
	};

	const totalPages = Math.ceil(totalUsers / limit);

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<LoadingSpinner className="h-8 w-8" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 dark:text-white">
						User Management
					</h1>
					<p className="text-gray-600 dark:text-gray-400 mt-1">
						Manage user accounts and permissions
					</p>
				</div>
				<div className="flex space-x-2">
					<Button onClick={handleCreateUser} variant="default">
						<UserPlus className="h-4 w-4 mr-2" />
						Create User
					</Button>
					<Button onClick={handleRefresh} disabled={refreshing} variant="outline">
						<RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
						Refresh
					</Button>
				</div>
			</div>

			{/* Search and Filters */}
			<Card>
				<CardContent className="pt-6">
					<div className="flex space-x-4">
						<div className="flex-1">
							<Input
								placeholder="Search users..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
							/>
						</div>
						<Button onClick={handleSearch}>
							<Search className="h-4 w-4 mr-2" />
							Search
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Users Table */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Users className="h-5 w-5 mr-2" />
						Users ({totalUsers})
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="overflow-x-auto">
						<table className="w-full">
							<thead>
								<tr className="border-b border-gray-200 dark:border-gray-700">
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										User
									</th>
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										Provider
									</th>
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										Role
									</th>
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										Status
									</th>
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										Created
									</th>
									<th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
										Actions
									</th>
								</tr>
							</thead>
							<tbody>
								{users.map((user) => (
									<tr
										key={user.id}
										className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800"
									>
										<td className="py-3 px-4">
											<div className="flex items-center">
												{user.role === Role.ADMIN ? (
													<Shield className="h-4 w-4 text-red-500 mr-2" />
												) : (
													<User className="h-4 w-4 text-gray-400 mr-2" />
												)}
												<span className="font-medium">
													{user.username || 'Anonymous'}
												</span>
											</div>
										</td>
										<td className="py-3 px-4">
											<Badge variant="outline">{user.provider}</Badge>
										</td>
										<td className="py-3 px-4">
											<Select
												value={user.role}
												onValueChange={(value: Role) =>
													updateUserRole(user.id, value)
												}
												disabled={updatingUsers.has(user.id)}
											>
												<SelectTrigger className="w-32">
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value={Role.USER}>User</SelectItem>
													<SelectItem value={Role.ADMIN}>
														Admin
													</SelectItem>
												</SelectContent>
											</Select>
										</td>
										<td className="py-3 px-4">
											<Badge className={getStatusBadgeColor(user.disabled)}>
												{user.disabled ? 'Disabled' : 'Active'}
											</Badge>
										</td>
										<td className="py-3 px-4 text-sm text-gray-600 dark:text-gray-400">
											{new Date(user.created_at).toLocaleDateString()}
										</td>
										<td className="py-3 px-4">
											<div className="flex space-x-2">
												{/* Edit Button */}
												<Button
													size="sm"
													variant="outline"
													onClick={() => handleEditUser(user)}
													disabled={updatingUsers.has(user.id)}
													title="Edit User"
												>
													<Edit className="h-3 w-3" />
												</Button>

												{/* Change Password Button - Only for USERNAME_PASSWORD users */}
												{user.provider === 'USERNAME_PASSWORD' && (
													<Button
														size="sm"
														variant="outline"
														onClick={() => setChangePasswordUser(user)}
														disabled={updatingUsers.has(user.id)}
														title="Change Password"
													>
														<Key className="h-3 w-3" />
													</Button>
												)}

												{/* Enable/Disable Button */}
												<Button
													size="sm"
													variant={
														user.disabled ? 'default' : 'destructive'
													}
													onClick={() =>
														toggleUserStatus(user.id, user.disabled)
													}
													disabled={updatingUsers.has(user.id)}
													title={
														user.disabled
															? 'Enable User'
															: 'Disable User'
													}
												>
													{updatingUsers.has(user.id) ? (
														<LoadingSpinner className="h-3 w-3" />
													) : user.disabled ? (
														<CheckCircle className="h-3 w-3" />
													) : (
														<Ban className="h-3 w-3" />
													)}
												</Button>

												{/* Delete Button */}
												<Button
													size="sm"
													variant="destructive"
													onClick={() => handleDeleteUser(user)}
													disabled={updatingUsers.has(user.id)}
													title="Delete User"
												>
													<Trash2 className="h-3 w-3" />
												</Button>
											</div>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>

					{/* Pagination */}
					{totalPages > 1 && (
						<div className="flex justify-between items-center mt-6">
							<div className="text-sm text-gray-600 dark:text-gray-400">
								Showing {(currentPage - 1) * limit + 1} to{' '}
								{Math.min(currentPage * limit, totalUsers)} of {totalUsers} users
							</div>
							<div className="flex space-x-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
									disabled={currentPage === 1}
								>
									Previous
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										setCurrentPage((prev) => Math.min(totalPages, prev + 1))
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Create User Modal */}
			{showCreateForm && (
				<div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/20 backdrop-blur-sm">
					<CreateUserForm onSuccess={handleFormSuccess} onCancel={handleFormCancel} />
				</div>
			)}

			{/* Edit User Modal */}
			{showEditForm && selectedUser && (
				<div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/20 backdrop-blur-sm">
					<EditUserForm
						user={selectedUser}
						onSuccess={handleFormSuccess}
						onCancel={handleFormCancel}
					/>
				</div>
			)}

			{/* Delete Confirmation Dialog */}
			{showDeleteDialog && selectedUser && (
				<div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/20 backdrop-blur-sm">
					<Card className="w-full max-w-md mx-auto">
						<CardHeader>
							<CardTitle className="flex items-center text-red-600">
								<AlertTriangle className="h-5 w-5 mr-2" />
								Delete User
							</CardTitle>
						</CardHeader>
						<CardContent>
							<p className="text-gray-600 dark:text-gray-400 mb-4">
								Are you sure you want to delete user{' '}
								<strong>{selectedUser.username || 'Anonymous'}</strong>?
							</p>
							<p className="text-sm text-red-600 dark:text-red-400 mb-6">
								This action will permanently delete the user and all related data.
								This cannot be undone.
							</p>
							<div className="flex space-x-2">
								<Button
									variant="destructive"
									onClick={confirmDeleteUser}
									disabled={updatingUsers.has(selectedUser.id)}
									className="flex-1"
								>
									{updatingUsers.has(selectedUser.id) ? (
										<>
											<LoadingSpinner className="h-4 w-4 mr-2" />
											Deleting...
										</>
									) : (
										'Delete User'
									)}
								</Button>
								<Button
									variant="outline"
									onClick={() => {
										setShowDeleteDialog(false);
										setSelectedUser(null);
									}}
									className="flex-1"
								>
									Cancel
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Change Password Modal */}
			{changePasswordUser && (
				<div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/20 backdrop-blur-sm">
					<ChangeUserPasswordForm
						userId={changePasswordUser.id}
						username={changePasswordUser.username || 'Unknown User'}
						onSuccess={() => {
							setChangePasswordUser(null);
							// Optionally refresh users list
							fetchUsers(currentPage, searchTerm);
						}}
						onCancel={() => setChangePasswordUser(null)}
					/>
				</div>
			)}
		</div>
	);
}
