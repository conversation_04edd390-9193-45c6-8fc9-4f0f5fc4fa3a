'use client';

import { useState, useEffect, useCallback } from 'react';
import {
	<PERSON>,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
	Loading<PERSON><PERSON>ner,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	Users,
	FolderOpen,
	MessageSquare,
	Activity,
	TrendingUp,
	TrendingDown,
	AlertTriangle,
	CheckCircle,
	RefreshCw,
	Trash2,
	Database,
	Zap,
	Brain,
	Clock,
	DollarSign,
	BarChart3,
	Layers,
	Server,
	Cpu,
	HardDrive,
	MemoryStick,
	Target,
	Timer,
	BookOpen,
	UserPlus,
	UserCheck,
	UserX,
	FileText,
	Gauge,
} from 'lucide-react';

interface AdminStats {
	users: {
		total: number;
		active: number;
		admins: number;
		newThisWeek: number;
		newToday: number;
		disabled: number;
	};
	collections: {
		total: number;
		public: number;
		private: number;
		createdThisWeek: number;
		averageWordsPerCollection: number;
	};
	words: {
		total: number;
		byLanguage: Record<string, number>;
		createdThisWeek: number;
		averageDefinitionsPerWord: number;
	};
	feedback: {
		total: number;
		pending: number;
		resolved: number;
		newThisWeek: number;
	};
	practice: {
		totalSessions: number;
		activeUsersToday: number;
		averageSessionDuration: number;
		completionRate: number;
	};
	llm: {
		totalRequests: number;
		totalTokensUsed: number;
		totalCost: number;
		requestsToday: number;
		tokensToday: number;
		costToday: number;
		averageResponseTime: number;
		errorRate: number;
		topModels: Array<{ model: string; usage: number; cost: number }>;
	};
	cache: {
		hitRate: number;
		missRate: number;
		totalKeys: number;
		memoryUsage: number;
		evictions: number;
		averageResponseTime: number;
		topCachedResources: Array<{ resource: string; hits: number; hitRate: number }>;
	};
	system: {
		cacheHitRate: number;
		apiResponseTime: number;
		errorRate: number;
		uptime: number;
		memoryUsage: number;
		cpuUsage: number;
		diskUsage: number;
	};
}

interface SystemHealth {
	status: string;
	details: Record<string, any>;
}

export function AdminDashboardOverview() {
	const [stats, setStats] = useState<AdminStats | null>(null);
	const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
	const [loading, setLoading] = useState(true);
	const [refreshing, setRefreshing] = useState(false);
	const [clearingCache, setClearingCache] = useState(false);
	const { showSuccess, showError } = useToast();

	const fetchDashboardData = useCallback(
		async (isRefresh = false) => {
			if (isRefresh) {
				setRefreshing(true);
			} else {
				setLoading(true);
			}

			try {
				const response = await fetch('/api/admin/dashboard', {
					credentials: 'include',
				});

				if (!response.ok) {
					throw new Error('Failed to fetch dashboard data');
				}

				const data = await response.json();

				if (data.success) {
					setStats(data.data.stats);
					setSystemHealth(data.data.systemHealth);
				} else {
					throw new Error(data.error || 'Failed to fetch dashboard data');
				}
			} catch (error) {
				showError(new Error('Failed to load dashboard data'));
			} finally {
				setLoading(false);
				setRefreshing(false);
			}
		},
		[showError]
	);

	useEffect(() => {
		fetchDashboardData();
	}, [fetchDashboardData]);

	const handleRefresh = () => {
		fetchDashboardData(true);
	};

	const handleClearCache = async () => {
		setClearingCache(true);
		try {
			const response = await fetch('/api/admin/cache/clear', {
				method: 'POST',
				credentials: 'include',
			});

			if (!response.ok) {
				throw new Error('Failed to clear cache');
			}

			const data = await response.json();
			showSuccess('Cache cleared successfully');

			// Refresh dashboard data after clearing cache
			fetchDashboardData(true);
		} catch (error) {
			showError(error instanceof Error ? error.message : 'Failed to clear cache');
		} finally {
			setClearingCache(false);
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<LoadingSpinner className="h-8 w-8" />
			</div>
		);
	}

	if (!stats || !systemHealth) {
		return (
			<div className="text-center py-12">
				<AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
				<p className="text-gray-600 dark:text-gray-400">Failed to load dashboard data</p>
				<Button onClick={handleRefresh} className="mt-4">
					Try Again
				</Button>
			</div>
		);
	}

	const getHealthStatusColor = (status: string) => {
		switch (status.toLowerCase()) {
			case 'healthy':
				return 'text-green-600 bg-green-100 dark:bg-green-900';
			case 'warning':
				return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900';
			case 'error':
				return 'text-red-600 bg-red-100 dark:bg-red-900';
			default:
				return 'text-gray-600 bg-gray-100 dark:bg-gray-900';
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div className="space-y-1">
					<h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
					<p className="text-muted-foreground">System overview and key metrics</p>
				</div>
				<div className="flex gap-2">
					<Button
						onClick={handleRefresh}
						disabled={refreshing || clearingCache}
						variant="outline"
						size="sm"
					>
						<RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
						Refresh
					</Button>
				</div>
			</div>

			{/* System Health */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Activity className="h-5 w-5 mr-2" />
						System Health
					</CardTitle>
					<CardDescription>Current system status and performance metrics</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="flex items-center space-x-4">
						<Badge className={getHealthStatusColor(systemHealth.status)}>
							{systemHealth.status.toUpperCase()}
						</Badge>
						<div className="flex space-x-6 text-sm">
							<div>
								<span className="text-gray-600 dark:text-gray-400">Database:</span>
								<span className="ml-2 font-medium">
									{systemHealth.details.database}
								</span>
							</div>
							<div>
								<span className="text-gray-600 dark:text-gray-400">Cache:</span>
								<span className="ml-2 font-medium">
									{systemHealth.details.cache}
								</span>
							</div>
							<div>
								<span className="text-gray-600 dark:text-gray-400">
									AI Services:
								</span>
								<span className="ml-2 font-medium">
									{systemHealth.details.ai_services}
								</span>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Overview Stats Grid */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				{/* Users Stats */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Users</CardTitle>
						<Users className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.users.total}</div>
						<div className="text-xs text-muted-foreground mt-1">
							<span className="text-green-600">+{stats.users.newToday}</span> today,{' '}
							<span className="text-blue-600">+{stats.users.newThisWeek}</span> this
							week
						</div>
						<div className="text-xs text-muted-foreground mt-2">
							Active: {stats.users.active} | Admins: {stats.users.admins} | Disabled:{' '}
							{stats.users.disabled}
						</div>
					</CardContent>
				</Card>

				{/* Collections Stats */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Collections</CardTitle>
						<FolderOpen className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.collections.total}</div>
						<div className="text-xs text-muted-foreground mt-1">
							<span className="text-green-600">
								+{stats.collections.createdThisWeek}
							</span>{' '}
							this week
						</div>
						<div className="text-xs text-muted-foreground mt-2">
							Avg words: {stats.collections.averageWordsPerCollection.toFixed(1)}
						</div>
					</CardContent>
				</Card>

				{/* Words Stats */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Words</CardTitle>
						<BookOpen className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.words.total}</div>
						<div className="text-xs text-muted-foreground mt-1">
							<span className="text-green-600">+{stats.words.createdThisWeek}</span>{' '}
							this week
						</div>
						<div className="text-xs text-muted-foreground mt-2">
							Avg definitions: {stats.words.averageDefinitionsPerWord.toFixed(1)}
						</div>
					</CardContent>
				</Card>

				{/* LLM Usage Stats */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">LLM Usage</CardTitle>
						<Brain className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">${stats.llm.totalCost.toFixed(2)}</div>
						<div className="text-xs text-muted-foreground mt-1">
							<span className="text-blue-600">${stats.llm.costToday.toFixed(2)}</span>{' '}
							today
						</div>
						<div className="text-xs text-muted-foreground mt-2">
							{stats.llm.totalTokensUsed.toLocaleString()} tokens total
						</div>
					</CardContent>
				</Card>
			</div>

			{/* LLM Monitoring Section */}
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<h2 className="text-2xl font-bold text-gray-900 dark:text-white">
						LLM Token Monitoring
					</h2>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
					{/* Total Requests */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Total Requests</CardTitle>
							<BarChart3 className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats.llm.totalRequests.toLocaleString()}
							</div>
							<div className="text-xs text-muted-foreground mt-1">
								<span className="text-green-600">{stats.llm.requestsToday}</span>{' '}
								today
							</div>
						</CardContent>
					</Card>

					{/* Token Usage */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Token Usage</CardTitle>
							<Zap className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{(stats.llm.totalTokensUsed / 1000000).toFixed(1)}M
							</div>
							<div className="text-xs text-muted-foreground mt-1">
								<span className="text-blue-600">
									{stats.llm.tokensToday.toLocaleString()}
								</span>{' '}
								today
							</div>
						</CardContent>
					</Card>

					{/* Average Response Time */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
							<Timer className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats.llm.averageResponseTime}ms
							</div>
							<div className="text-xs text-muted-foreground mt-1">
								Error rate: {(stats.llm.errorRate * 100).toFixed(2)}%
							</div>
						</CardContent>
					</Card>

					{/* Cost Efficiency */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Cost per 1K Tokens
							</CardTitle>
							<DollarSign className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								$
								{((stats.llm.totalCost / stats.llm.totalTokensUsed) * 1000).toFixed(
									4
								)}
							</div>
							<div className="text-xs text-muted-foreground mt-1">
								Efficiency metric
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Top Models Usage */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center">
							<Brain className="h-5 w-5 mr-2" />
							Top Models Usage
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{stats.llm.topModels.map((model, index) => (
								<div
									key={model.model}
									className="flex items-center justify-between"
								>
									<div className="flex items-center space-x-3">
										<Badge variant="outline">#{index + 1}</Badge>
										<span className="font-medium">{model.model}</span>
									</div>
									<div className="text-right">
										<div className="text-sm font-medium">
											{model.usage.toLocaleString()} requests
										</div>
										<div className="text-xs text-muted-foreground">
											${model.cost.toFixed(2)} cost
										</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Cache Performance Section */}
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<h2 className="text-2xl font-bold text-gray-900 dark:text-white">
						Cache Performance
					</h2>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
					{/* Hit Rate */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Hit Rate</CardTitle>
							<Target className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats?.cache?.hitRate
									? (stats.cache.hitRate * 100).toFixed(1)
									: '0.0'}
								%
							</div>
							<div className="text-xs text-muted-foreground mt-1">
								Miss rate:{' '}
								{stats?.cache?.missRate
									? (stats.cache.missRate * 100).toFixed(1)
									: '0.0'}
								%
							</div>
						</CardContent>
					</Card>

					{/* Total Keys */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Total Keys</CardTitle>
							<Database className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats?.cache?.totalKeys
									? stats.cache.totalKeys.toLocaleString()
									: '0'}
							</div>
							<div className="text-xs text-muted-foreground mt-1">
								{stats?.cache?.evictions || 0} evictions
							</div>
						</CardContent>
					</Card>

					{/* Memory Usage */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
							<MemoryStick className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats?.cache?.memoryUsage
									? (stats.cache.memoryUsage / 1024 / 1024).toFixed(1)
									: '0.0'}
								MB
							</div>
							<div className="text-xs text-muted-foreground mt-1">Cache memory</div>
						</CardContent>
					</Card>

					{/* Response Time */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
							<Gauge className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats?.cache?.averageResponseTime || 0}ms
							</div>
							<div className="text-xs text-muted-foreground mt-1">
								Cache performance
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Top Cached Resources */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center">
							<Layers className="h-5 w-5 mr-2" />
							Top Cached Resources
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{stats?.cache?.topCachedResources?.length > 0 ? (
								stats.cache.topCachedResources.map((resource, index) => (
									<div
										key={resource.resource}
										className="flex items-center justify-between"
									>
										<div className="flex items-center space-x-3">
											<Badge variant="outline">#{index + 1}</Badge>
											<span className="font-medium">{resource.resource}</span>
										</div>
										<div className="text-right">
											<div className="text-sm font-medium">
												{resource.hits.toLocaleString()} hits
											</div>
											<div className="text-xs text-muted-foreground">
												{(resource.hitRate * 100).toFixed(1)}% hit rate
											</div>
										</div>
									</div>
								))
							) : (
								<div className="text-center py-4 text-muted-foreground">
									<p className="text-sm">No cached resources data available</p>
								</div>
							)}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* System Management */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Cache Management */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center">
							<Database className="h-5 w-5 mr-2" />
							Cache Management
						</CardTitle>
						<CardDescription>Manage application cache and performance</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium">Cache Status</p>
								<p className="text-xs text-muted-foreground">
									Hit Rate:{' '}
									{stats?.system?.cacheHitRate
										? (stats.system.cacheHitRate * 100).toFixed(1)
										: '0.0'}
									%
								</p>
							</div>
							<Badge variant="outline" className="text-green-600">
								Active
							</Badge>
						</div>

						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium">Clear All Cache</p>
								<p className="text-xs text-muted-foreground">
									Remove all cached data to free memory
								</p>
							</div>
							<Button
								onClick={handleClearCache}
								disabled={clearingCache}
								variant="destructive"
								size="sm"
							>
								{clearingCache ? (
									<>
										<Clock className="h-4 w-4 mr-2 animate-spin" />
										Clearing...
									</>
								) : (
									<>
										<Trash2 className="h-4 w-4 mr-2" />
										Clear
									</>
								)}
							</Button>
						</div>
					</CardContent>
				</Card>

				{/* System Performance */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center">
							<Activity className="h-5 w-5 mr-2" />
							Performance Metrics
						</CardTitle>
						<CardDescription>Real-time system performance indicators</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-3">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">API Response Time</span>
								<span className="text-sm text-muted-foreground">
									{stats ? stats.system.apiResponseTime.toFixed(0) : '0'}ms
								</span>
							</div>

							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Cache Hit Rate</span>
								<span className="text-sm text-muted-foreground">
									{stats?.system?.cacheHitRate
										? (stats.system.cacheHitRate * 100).toFixed(1)
										: '0.0'}
									%
								</span>
							</div>

							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Error Rate</span>
								<span className="text-sm text-muted-foreground">
									{stats ? (stats.system.errorRate * 100).toFixed(2) : '0'}%
								</span>
							</div>
						</div>

						<div className="pt-2 border-t">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">System Status</span>
								<Badge
									className={
										systemHealth
											? getHealthStatusColor(systemHealth.status)
											: 'bg-gray-100'
									}
								>
									{systemHealth ? systemHealth.status.toUpperCase() : 'UNKNOWN'}
								</Badge>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
