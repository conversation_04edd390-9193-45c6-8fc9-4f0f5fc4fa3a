'use client';

import { useState, useEffect, useCallback } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	LoadingSpinner,
	Badge,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	FileText,
	Search,
	RefreshCw,
	Filter,
	Calendar,
	User,
	Shield,
	Activity,
} from 'lucide-react';

interface AuditEvent {
	id: string;
	action: string;
	resource: string;
	resource_id?: string;
	user_id?: string;
	admin_id?: string;
	details: Record<string, any>;
	ip_address?: string;
	user_agent?: string;
	timestamp: string;
}

interface AuditLogsResponse {
	logs: AuditEvent[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

export function AuditLogs() {
	const [logs, setLogs] = useState<AuditEvent[]>([]);
	const [loading, setLoading] = useState(true);
	const [refreshing, setRefreshing] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalLogs, setTotalLogs] = useState(0);
	const [totalPages, setTotalPages] = useState(0);

	// Filters
	const [actionFilter, setActionFilter] = useState('');
	const [resourceFilter, setResourceFilter] = useState('');
	const [userIdFilter, setUserIdFilter] = useState('');

	const { showError } = useToast();

	const limit = 20;

	const fetchAuditLogs = useCallback(
		async (page = 1, isRefresh = false) => {
			if (isRefresh) {
				setRefreshing(true);
			} else {
				setLoading(true);
			}

			try {
				const params = new URLSearchParams({
					page: page.toString(),
					limit: limit.toString(),
				});

				if (actionFilter && actionFilter !== 'all') params.append('action', actionFilter);
				if (resourceFilter && resourceFilter !== 'all')
					params.append('resource', resourceFilter);
				if (userIdFilter) params.append('user_id', userIdFilter);

				const response = await fetch(`/api/admin/audit?${params}`, {
					credentials: 'include',
				});

				if (!response.ok) {
					throw new Error('Failed to fetch audit logs');
				}

				const data = await response.json();

				if (data.success) {
					setLogs(data.data.logs);
					setTotalLogs(data.data.total);
					setCurrentPage(data.data.page);
					setTotalPages(data.data.totalPages);
				} else {
					throw new Error(data.error || 'Failed to fetch audit logs');
				}
			} catch (error) {
				showError(new Error('Failed to load audit logs'));
			} finally {
				setLoading(false);
				setRefreshing(false);
			}
		},
		[actionFilter, resourceFilter, userIdFilter, showError]
	);

	useEffect(() => {
		fetchAuditLogs(currentPage);
	}, [currentPage, fetchAuditLogs]);

	const handleRefresh = () => {
		fetchAuditLogs(currentPage, true);
	};

	const handleFilter = () => {
		setCurrentPage(1);
		fetchAuditLogs(1);
	};

	const clearFilters = () => {
		setActionFilter('');
		setResourceFilter('');
		setUserIdFilter('');
		setCurrentPage(1);
		fetchAuditLogs(1);
	};

	const getActionBadgeColor = (action: string) => {
		if (action.includes('login') || action.includes('created')) {
			return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
		}
		if (action.includes('deleted') || action.includes('disabled')) {
			return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
		}
		if (action.includes('updated') || action.includes('enabled')) {
			return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
		}
		if (action.includes('error') || action.includes('warning')) {
			return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
		}
		return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
	};

	const getResourceIcon = (resource: string) => {
		switch (resource) {
			case 'user':
				return <User className="h-4 w-4" />;
			case 'admin':
				return <Shield className="h-4 w-4" />;
			case 'system':
				return <Activity className="h-4 w-4" />;
			default:
				return <FileText className="h-4 w-4" />;
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<LoadingSpinner className="h-8 w-8" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 dark:text-white">Audit Logs</h1>
					<p className="text-gray-600 dark:text-gray-400 mt-1">
						System activity and security audit trail
					</p>
				</div>
				<Button onClick={handleRefresh} disabled={refreshing} variant="outline">
					<RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
					Refresh
				</Button>
			</div>

			{/* Filters */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Filter className="h-5 w-5 mr-2" />
						Filters
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
						<div>
							<label
								htmlFor="action-filter"
								className="block text-sm font-medium mb-2"
							>
								Action
							</label>
							<Input
								id="action-filter"
								placeholder="Filter by action..."
								value={actionFilter}
								onChange={(e) => setActionFilter(e.target.value)}
							/>
						</div>
						<div>
							<label
								htmlFor="resource-filter"
								className="block text-sm font-medium mb-2"
							>
								Resource
							</label>
							<Select value={resourceFilter} onValueChange={setResourceFilter}>
								<SelectTrigger id="resource-filter">
									<SelectValue placeholder="All resources" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All resources</SelectItem>
									<SelectItem value="user">User</SelectItem>
									<SelectItem value="admin">Admin</SelectItem>
									<SelectItem value="collection">Collection</SelectItem>
									<SelectItem value="word">Word</SelectItem>
									<SelectItem value="feedback">Feedback</SelectItem>
									<SelectItem value="system">System</SelectItem>
								</SelectContent>
							</Select>
						</div>
						<div>
							<label
								htmlFor="user-id-filter"
								className="block text-sm font-medium mb-2"
							>
								User ID
							</label>
							<Input
								id="user-id-filter"
								placeholder="Filter by user ID..."
								value={userIdFilter}
								onChange={(e) => setUserIdFilter(e.target.value)}
							/>
						</div>
						<div className="flex items-end space-x-2">
							<Button onClick={handleFilter}>
								<Search className="h-4 w-4 mr-2" />
								Filter
							</Button>
							<Button onClick={clearFilters} variant="outline">
								Clear
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Audit Logs */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<FileText className="h-5 w-5 mr-2" />
						Audit Events ({totalLogs})
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{logs.map((log) => (
							<div
								key={log.id}
								className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
							>
								<div className="flex items-start justify-between">
									<div className="flex items-start space-x-3">
										<div className="p-2 bg-gray-100 dark:bg-gray-800 rounded">
											{getResourceIcon(log.resource)}
										</div>
										<div className="flex-1">
											<div className="flex items-center space-x-2 mb-2">
												<Badge className={getActionBadgeColor(log.action)}>
													{log.action}
												</Badge>
												<Badge variant="outline">{log.resource}</Badge>
												{log.resource_id && (
													<span className="text-xs text-gray-500">
														ID: {log.resource_id.slice(0, 8)}...
													</span>
												)}
											</div>
											<div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
												{JSON.stringify(log.details, null, 2)}
											</div>
											<div className="flex items-center space-x-4 text-xs text-gray-500">
												<div className="flex items-center">
													<Calendar className="h-3 w-3 mr-1" />
													{new Date(log.timestamp).toLocaleString()}
												</div>
												{log.user_id && (
													<div className="flex items-center">
														<User className="h-3 w-3 mr-1" />
														User: {log.user_id.slice(0, 8)}...
													</div>
												)}
												{log.admin_id && (
													<div className="flex items-center">
														<Shield className="h-3 w-3 mr-1" />
														Admin: {log.admin_id.slice(0, 8)}...
													</div>
												)}
												{log.ip_address && (
													<span>IP: {log.ip_address}</span>
												)}
											</div>
										</div>
									</div>
								</div>
							</div>
						))}
					</div>

					{/* Empty State */}
					{logs.length === 0 && (
						<div className="text-center py-12">
							<FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
							<p className="text-gray-600 dark:text-gray-400">No audit logs found</p>
						</div>
					)}

					{/* Pagination */}
					{totalPages > 1 && (
						<div className="flex justify-between items-center mt-6">
							<div className="text-sm text-gray-600 dark:text-gray-400">
								Showing {(currentPage - 1) * limit + 1} to{' '}
								{Math.min(currentPage * limit, totalLogs)} of {totalLogs} logs
							</div>
							<div className="flex space-x-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
									disabled={currentPage === 1}
								>
									Previous
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										setCurrentPage((prev) => Math.min(totalPages, prev + 1))
									}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
