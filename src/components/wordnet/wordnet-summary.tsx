'use client';

import { Network, BookOpen } from 'lucide-react';
import { WordNetData } from '@/models';
import { useTranslation } from '@/contexts/translation-context';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface WordNetSummaryProps {
	wordNetData: WordNetData | null | undefined;
	className?: string;
	showIcon?: boolean;
}

export function WordNetSummary({ wordNetData, className, showIcon = true }: WordNetSummaryProps) {
	const { t } = useTranslation();

	if (!wordNetData) {
		return null;
	}

	// Check if we have any meaningful data
	const hasData = wordNetData.synsets.length > 0 || 
					wordNetData.hypernyms.length > 0 || 
					wordNetData.hyponyms.length > 0 || 
					wordNetData.holonyms.length > 0 || 
					wordNetData.meronyms.length > 0 ||
					wordNetData.lemma;

	if (!hasData) {
		return null;
	}

	const totalRelations = wordNetData.hypernyms.length + 
						  wordNetData.hyponyms.length + 
						  wordNetData.holonyms.length + 
						  wordNetData.meronyms.length;

	return (
		<div className={`flex items-center gap-2 ${className}`}>
			{showIcon && <Network className="w-3 h-3 text-muted-foreground" />}
			
			<div className="flex items-center gap-1">
				{wordNetData.synsets.length > 0 && (
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<Badge variant="secondary" className="text-xs px-1.5 py-0.5">
									<BookOpen className="w-2.5 h-2.5 mr-1" />
									{wordNetData.synsets.length}
								</Badge>
							</TooltipTrigger>
							<TooltipContent>
								<p>{wordNetData.synsets.length} {t('wordnet.definitions').toLowerCase()}</p>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				)}

				{totalRelations > 0 && (
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<Badge variant="outline" className="text-xs px-1.5 py-0.5">
									<Network className="w-2.5 h-2.5 mr-1" />
									{totalRelations}
								</Badge>
							</TooltipTrigger>
							<TooltipContent>
								<p>{totalRelations} {t('wordnet.relatedTerms').toLowerCase()}</p>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				)}

				{wordNetData.lemma && (
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<Badge variant="outline" className="text-xs px-1.5 py-0.5 font-mono">
									{wordNetData.lemma}
								</Badge>
							</TooltipTrigger>
							<TooltipContent>
								<p>{t('wordnet.lemma')}: {wordNetData.lemma}</p>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				)}
			</div>
		</div>
	);
}
