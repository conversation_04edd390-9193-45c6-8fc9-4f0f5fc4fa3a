import { NextRequest } from 'next/server';
import { AuditService, AUDIT_ACTIONS, AUDIT_RESOURCES } from '@/backend/services/audit.service';

export interface RequestMetadata {
	ip_address?: string;
	user_agent?: string;
	method?: string;
	url?: string;
	headers?: Record<string, string>;
}

export interface AuditEventData {
	action: string;
	resource: string;
	resource_id?: string;
	user_id?: string;
	admin_id?: string;
	details?: Record<string, any>;
	metadata?: RequestMetadata;
}

export class AuditHelper {
	constructor(private readonly getAuditService: () => AuditService) {}

	/**
	 * Extract request metadata from NextRequest
	 */
	static extractRequestMetadata(request?: NextRequest): RequestMetadata {
		if (!request) {
			return {};
		}

		const ip_address = 
			request.headers.get('x-forwarded-for') ||
			request.headers.get('x-real-ip') ||
			request.headers.get('cf-connecting-ip') ||
			'unknown';

		const user_agent = request.headers.get('user-agent') || 'unknown';

		return {
			ip_address,
			user_agent,
			method: request.method,
			url: request.url,
			headers: {
				'content-type': request.headers.get('content-type') || '',
				'accept': request.headers.get('accept') || '',
				'origin': request.headers.get('origin') || '',
				'referer': request.headers.get('referer') || '',
			},
		};
	}

	/**
	 * Log an audit event with automatic error handling
	 */
	async logEvent(eventData: AuditEventData): Promise<void> {
		try {
			const auditService = this.getAuditService();
			await auditService.logEvent({
				action: eventData.action,
				resource: eventData.resource,
				resource_id: eventData.resource_id,
				user_id: eventData.user_id,
				admin_id: eventData.admin_id,
				details: {
					...eventData.details,
					metadata: eventData.metadata,
				},
				ip_address: eventData.metadata?.ip_address,
				user_agent: eventData.metadata?.user_agent,
			});
		} catch (error) {
			console.error('Failed to log audit event:', error, eventData);
		}
	}

	/**
	 * Log authentication events
	 */
	async logAuthEvent(
		action: typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS],
		userId?: string,
		details?: Record<string, any>,
		metadata?: RequestMetadata
	): Promise<void> {
		await this.logEvent({
			action,
			resource: AUDIT_RESOURCES.AUTH,
			user_id: userId,
			details,
			metadata,
		});
	}

	/**
	 * Log user management events
	 */
	async logUserEvent(
		action: typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS],
		userId: string,
		adminId?: string,
		details?: Record<string, any>,
		metadata?: RequestMetadata
	): Promise<void> {
		await this.logEvent({
			action,
			resource: AUDIT_RESOURCES.USER,
			resource_id: userId,
			user_id: userId,
			admin_id: adminId,
			details,
			metadata,
		});
	}

	/**
	 * Log collection events
	 */
	async logCollectionEvent(
		action: typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS],
		collectionId: string,
		userId?: string,
		details?: Record<string, any>,
		metadata?: RequestMetadata
	): Promise<void> {
		await this.logEvent({
			action,
			resource: AUDIT_RESOURCES.COLLECTION,
			resource_id: collectionId,
			user_id: userId,
			details,
			metadata,
		});
	}

	/**
	 * Log word events
	 */
	async logWordEvent(
		action: typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS],
		wordId: string,
		userId?: string,
		details?: Record<string, any>,
		metadata?: RequestMetadata
	): Promise<void> {
		await this.logEvent({
			action,
			resource: AUDIT_RESOURCES.WORD,
			resource_id: wordId,
			user_id: userId,
			details,
			metadata,
		});
	}

	/**
	 * Log feedback events
	 */
	async logFeedbackEvent(
		action: typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS],
		feedbackId: string,
		userId?: string,
		details?: Record<string, any>,
		metadata?: RequestMetadata
	): Promise<void> {
		await this.logEvent({
			action,
			resource: AUDIT_RESOURCES.FEEDBACK,
			resource_id: feedbackId,
			user_id: userId,
			details,
			metadata,
		});
	}

	/**
	 * Log system events
	 */
	async logSystemEvent(
		action: typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS],
		details?: Record<string, any>,
		metadata?: RequestMetadata
	): Promise<void> {
		await this.logEvent({
			action,
			resource: AUDIT_RESOURCES.SYSTEM,
			details,
			metadata,
		});
	}

	/**
	 * Log cache events
	 */
	async logCacheEvent(
		action: typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS],
		cacheKey?: string,
		userId?: string,
		details?: Record<string, any>,
		metadata?: RequestMetadata
	): Promise<void> {
		await this.logEvent({
			action,
			resource: AUDIT_RESOURCES.CACHE,
			resource_id: cacheKey,
			user_id: userId,
			details,
			metadata,
		});
	}

	/**
	 * Log LLM events
	 */
	async logLLMEvent(
		action: typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS],
		userId?: string,
		details?: Record<string, any>,
		metadata?: RequestMetadata
	): Promise<void> {
		await this.logEvent({
			action,
			resource: AUDIT_RESOURCES.LLM,
			user_id: userId,
			details,
			metadata,
		});
	}

	/**
	 * Log practice events
	 */
	async logPracticeEvent(
		action: typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS],
		collectionId?: string,
		userId?: string,
		details?: Record<string, any>,
		metadata?: RequestMetadata
	): Promise<void> {
		await this.logEvent({
			action,
			resource: AUDIT_RESOURCES.PRACTICE,
			resource_id: collectionId,
			user_id: userId,
			details,
			metadata,
		});
	}

	/**
	 * Log security events
	 */
	async logSecurityEvent(
		action: typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS],
		userId?: string,
		details?: Record<string, any>,
		metadata?: RequestMetadata
	): Promise<void> {
		await this.logEvent({
			action,
			resource: AUDIT_RESOURCES.SECURITY,
			user_id: userId,
			details,
			metadata,
		});
	}
}

/**
 * Create a new AuditHelper instance
 */
export function createAuditHelper(getAuditService: () => AuditService): AuditHelper {
	return new AuditHelper(getAuditService);
}

/**
 * Extract IP address from various headers
 */
export function extractIpAddress(request: NextRequest): string {
	return (
		request.headers.get('x-forwarded-for') ||
		request.headers.get('x-real-ip') ||
		request.headers.get('cf-connecting-ip') ||
		'unknown'
	);
}

/**
 * Extract user agent from request
 */
export function extractUserAgent(request: NextRequest): string {
	return request.headers.get('user-agent') || 'unknown';
}

/**
 * Create audit details with before/after values for updates
 */
export function createUpdateDetails(
	before: Record<string, any>,
	after: Record<string, any>,
	additionalDetails?: Record<string, any>
): Record<string, any> {
	return {
		before,
		after,
		changes: Object.keys(after).reduce((changes, key) => {
			if (before[key] !== after[key]) {
				changes[key] = { from: before[key], to: after[key] };
			}
			return changes;
		}, {} as Record<string, any>),
		...additionalDetails,
	};
}
