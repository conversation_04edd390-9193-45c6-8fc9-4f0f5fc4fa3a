import { FeedbackRepository } from '@/backend/repositories';
import { Feedback } from '@prisma/client';
import { AuditHelper } from '@/backend/utils/audit.helper';
import { AUDIT_ACTIONS } from './audit.service';
import { getPrismaClient } from '@/backend/wire';

export interface FeedbackService {
	createFeedback(
		message: string,
		userId: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<Feedback>;
	getAllFeedback(): Promise<any[]>;
}

export class FeedbackServiceImpl implements FeedbackService {
	constructor(
		private readonly getFeedbackRepository: () => FeedbackRepository,
		private readonly getAuditHelper: () => AuditHelper
	) {}

	async createFeedback(
		message: string,
		userId: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<Feedback> {
		const createdFeedback = await this.getFeedbackRepository().create({
			message,
			user: {
				connect: { id: userId },
			},
		});

		const feedback = await this.getFeedbackRepository().findById(createdFeedback.id);
		if (!feedback) {
			throw new Error(
				`Failed to retrieve created feedback with ID ${createdFeedback.id} including user relation.`
			);
		}

		// Log audit event
		try {
			const auditHelper = this.getAuditHelper();
			await auditHelper.logFeedbackEvent(
				AUDIT_ACTIONS.FEEDBACK_CREATED,
				feedback.id,
				userId,
				{
					message_length: message.length,
					message_preview: message.substring(0, 100),
				},
				{ ip_address: ipAddress, user_agent: userAgent }
			);
		} catch (error) {
			console.error('Failed to log audit event for feedback creation:', error);
		}

		return feedback;
	}

	async getAllFeedback(): Promise<any[]> {
		try {
			// Use Prisma client directly to get feedback with user information
			const prisma = getPrismaClient();
			return await prisma.feedback.findMany({
				include: {
					user: {
						select: {
							id: true,
							username: true,
							provider: true,
							provider_id: true,
							created_at: true,
						},
					},
				},
				orderBy: {
					created_at: 'desc',
				},
			});
		} catch (error) {
			console.error('Error in getAllFeedback:', error);
			// Return empty array on error to avoid breaking the API
			return [];
		}
	}
}
