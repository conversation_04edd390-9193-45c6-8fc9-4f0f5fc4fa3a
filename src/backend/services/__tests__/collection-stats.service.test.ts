import { jest, describe, beforeEach, it, expect } from '@jest/globals';
import { CollectionStatsService, CollectionStatsServiceImpl } from '../collection-stats.service';
import { CollectionStatsRepository } from '../../repositories/collection-stats.repository';
import { CollectionStats } from '@prisma/client';

// Mock dependencies
const mockCollectionStatsRepository = {
	getStatsByCollectionAndDateRange: jest.fn(),
	getStatsByCollectionAndDate: jest.fn(),
	incrementWordsReviewed: jest.fn(),
	incrementQAPracticeSubmissions: jest.fn(),
	incrementParagraphPracticeSubmissions: jest.fn(),
	createOrUpdateStats: jest.fn(),
} as jest.Mocked<CollectionStatsRepository>;

// Helper function to create mock stats
function createMockStats(overrides: Partial<CollectionStats> = {}): CollectionStats {
	return {
		id: 'stats-1',
		collection_id: 'collection-id',
		user_id: 'user-id',
		date: new Date(),
		words_reviewed_count: 0,
		qa_practice_submissions: 0,
		paragraph_practice_submissions: 0,
		created_at: new Date(),
		updated_at: new Date(),
		...overrides,
	};
}

describe('CollectionStatsService', () => {
	let collectionStatsService: CollectionStatsService;

	beforeEach(() => {
		jest.clearAllMocks();
		collectionStatsService = new CollectionStatsServiceImpl(
			() => mockCollectionStatsRepository
		);
	});

	describe('getStatsForCollection', () => {
		it('should return stats for default 7 days', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';
			// Create dates within the 7-day range
			const today = new Date();
			const yesterday = new Date(today);
			yesterday.setDate(today.getDate() - 1);

			const mockStats: CollectionStats[] = [
				{
					id: 'stats-1',
					collection_id: collectionId,
					user_id: userId,
					date: yesterday,
					words_reviewed_count: 5,
					qa_practice_submissions: 2,
					paragraph_practice_submissions: 1,
					created_at: new Date(),
					updated_at: new Date(),
				},
				{
					id: 'stats-2',
					collection_id: collectionId,
					user_id: userId,
					date: today,
					words_reviewed_count: 3,
					qa_practice_submissions: 1,
					paragraph_practice_submissions: 2,
					created_at: new Date(),
					updated_at: new Date(),
				},
			];

			mockCollectionStatsRepository.getStatsByCollectionAndDateRange.mockResolvedValue(
				mockStats
			);

			const result = await collectionStatsService.getStatsForCollection(collectionId, userId);

			expect(
				mockCollectionStatsRepository.getStatsByCollectionAndDateRange
			).toHaveBeenCalledWith(
				collectionId,
				userId,
				expect.any(Date), // startDate (7 days ago)
				expect.any(Date) // endDate (today)
			);
			expect(result).toHaveLength(7); // Service returns 7 days by default
			// Find the day with actual data
			const dayWithData = result.find((r) => r.words_reviewed_count > 0);
			expect(dayWithData).toMatchObject({
				words_reviewed_count: 5,
				qa_practice_submissions: 2,
				paragraph_practice_submissions: 1,
			});
		});

		it('should return stats for custom date range', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';
			const days = 14;
			const mockStats: CollectionStats[] = [];

			mockCollectionStatsRepository.getStatsByCollectionAndDateRange.mockResolvedValue(
				mockStats
			);

			const result = await collectionStatsService.getStatsForCollection(
				collectionId,
				userId,
				days
			);

			expect(
				mockCollectionStatsRepository.getStatsByCollectionAndDateRange
			).toHaveBeenCalledWith(
				collectionId,
				userId,
				expect.any(Date), // startDate (14 days ago)
				expect.any(Date) // endDate (today)
			);
			expect(result).toHaveLength(14); // Service returns all 14 days in range
			// All days should have zero stats since mock returns empty array
			expect(result.every((r) => r.words_reviewed_count === 0)).toBe(true);
		});

		it('should handle repository errors', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';

			mockCollectionStatsRepository.getStatsByCollectionAndDateRange.mockRejectedValue(
				new Error('Database error')
			);

			await expect(
				collectionStatsService.getStatsForCollection(collectionId, userId)
			).rejects.toThrow('Database error');
		});
	});

	describe('trackWordReview', () => {
		it('should track word review with default count', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';
			const mockStats = createMockStats({
				collection_id: collectionId,
				user_id: userId,
				words_reviewed_count: 1,
			});

			mockCollectionStatsRepository.incrementWordsReviewed.mockResolvedValue(mockStats);

			await collectionStatsService.trackWordReview(collectionId, userId);

			expect(mockCollectionStatsRepository.incrementWordsReviewed).toHaveBeenCalledWith(
				collectionId,
				userId,
				expect.any(Date),
				1 // default count
			);
		});

		it('should track word review with custom count', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';
			const count = 5;
			const mockStats = createMockStats({
				collection_id: collectionId,
				user_id: userId,
				words_reviewed_count: count,
			});

			mockCollectionStatsRepository.incrementWordsReviewed.mockResolvedValue(mockStats);

			await collectionStatsService.trackWordReview(collectionId, userId, count);

			expect(mockCollectionStatsRepository.incrementWordsReviewed).toHaveBeenCalledWith(
				collectionId,
				userId,
				expect.any(Date),
				count
			);
		});
	});

	describe('trackQAPracticeSubmission', () => {
		it('should track QA practice submission with default count', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';
			const mockStats = createMockStats({
				collection_id: collectionId,
				user_id: userId,
				qa_practice_submissions: 1,
			});

			mockCollectionStatsRepository.incrementQAPracticeSubmissions.mockResolvedValue(
				mockStats
			);

			await collectionStatsService.trackQAPracticeSubmission(collectionId, userId);

			expect(
				mockCollectionStatsRepository.incrementQAPracticeSubmissions
			).toHaveBeenCalledWith(
				collectionId,
				userId,
				expect.any(Date),
				1 // default count
			);
		});

		it('should track QA practice submission with custom count', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';
			const count = 3;
			const mockStats = createMockStats({
				collection_id: collectionId,
				user_id: userId,
				qa_practice_submissions: count,
			});

			mockCollectionStatsRepository.incrementQAPracticeSubmissions.mockResolvedValue(
				mockStats
			);

			await collectionStatsService.trackQAPracticeSubmission(collectionId, userId, count);

			expect(
				mockCollectionStatsRepository.incrementQAPracticeSubmissions
			).toHaveBeenCalledWith(collectionId, userId, expect.any(Date), count);
		});
	});

	describe('trackParagraphPracticeSubmission', () => {
		it('should track paragraph practice submission with default count', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';
			const mockStats = createMockStats({
				collection_id: collectionId,
				user_id: userId,
				paragraph_practice_submissions: 1,
			});

			mockCollectionStatsRepository.incrementParagraphPracticeSubmissions.mockResolvedValue(
				mockStats
			);

			await collectionStatsService.trackParagraphPracticeSubmission(collectionId, userId);

			expect(
				mockCollectionStatsRepository.incrementParagraphPracticeSubmissions
			).toHaveBeenCalledWith(
				collectionId,
				userId,
				expect.any(Date),
				1 // default count
			);
		});

		it('should track paragraph practice submission with custom count', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';
			const count = 2;
			const mockStats = createMockStats({
				collection_id: collectionId,
				user_id: userId,
				paragraph_practice_submissions: count,
			});

			mockCollectionStatsRepository.incrementParagraphPracticeSubmissions.mockResolvedValue(
				mockStats
			);

			await collectionStatsService.trackParagraphPracticeSubmission(
				collectionId,
				userId,
				count
			);

			expect(
				mockCollectionStatsRepository.incrementParagraphPracticeSubmissions
			).toHaveBeenCalledWith(collectionId, userId, expect.any(Date), count);
		});
	});

	describe('getTodayStats', () => {
		it('should return today stats if exists', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';
			const today = new Date();
			const mockStats = createMockStats({
				collection_id: collectionId,
				user_id: userId,
				date: today,
				words_reviewed_count: 5,
				qa_practice_submissions: 2,
				paragraph_practice_submissions: 1,
			});

			mockCollectionStatsRepository.getStatsByCollectionAndDate.mockResolvedValue(mockStats);

			const result = await collectionStatsService.getTodayStats(collectionId, userId);

			expect(mockCollectionStatsRepository.getStatsByCollectionAndDate).toHaveBeenCalledWith(
				collectionId,
				userId,
				expect.any(Date)
			);
			expect(result).toMatchObject({
				date: expect.any(String),
				words_reviewed_count: 5,
				qa_practice_submissions: 2,
				paragraph_practice_submissions: 1,
			});
		});

		it('should return zero stats if no data for today', async () => {
			const collectionId = 'collection-id';
			const userId = 'user-id';

			mockCollectionStatsRepository.getStatsByCollectionAndDate.mockResolvedValue(null);

			const result = await collectionStatsService.getTodayStats(collectionId, userId);

			expect(result).toMatchObject({
				date: expect.any(String),
				words_reviewed_count: 0,
				qa_practice_submissions: 0,
				paragraph_practice_submissions: 0,
			});
		});
	});
});
