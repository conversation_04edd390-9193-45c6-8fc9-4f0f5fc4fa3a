import { CollectionService, CollectionServiceImpl } from '../collection.service';
import { CollectionRepository } from '@/backend/repositories';
import { WordService } from '../word.service';
import { AuditService } from '../audit.service';
import { Language, Difficulty } from '@prisma/client';
import { mockCollection, mockWordDetail, mockUser } from '@/test/fixtures';
import { ServerError } from '@/lib/error-handling';
import { jest, describe, beforeEach, it, expect } from '@jest/globals';

// Mock dependencies
const mockCollectionRepository = {
	findById: jest.fn(),
	findOne: jest.fn(),
	find: jest.fn(),
	create: jest.fn(),
	update: jest.fn(),
	delete: jest.fn(),
	findUserCollections: jest.fn(),
	findByUserIdAndName: jest.fn(),
	addWordsToCollection: jest.fn(),
	removeWordsFromCollection: jest.fn(),
} as jest.Mocked<CollectionRepository>;

const mockWordService = {
	findByIds: jest.fn(),
	findWordsByIds: jest.fn(),
	findByTerms: jest.fn(),
	createWords: jest.fn(),
	searchWords: jest.fn(),
} as jest.Mocked<Partial<WordService>>;

const mockAuditService = {
	logAction: jest.fn(),
} as jest.Mocked<Partial<AuditService>>;

const mockLLMService = {
	generateWordDetails: jest.fn(),
	generateRandomTerms: jest.fn(),
} as any;

describe('CollectionService', () => {
	let collectionService: CollectionService;

	beforeEach(() => {
		jest.clearAllMocks();
		collectionService = new CollectionServiceImpl(
			() => mockCollectionRepository,
			() => Promise.resolve(mockLLMService),
			() => mockWordService as WordService,
			() => mockAuditService as AuditService,
			() => ({ logCollectionEvent: jest.fn() } as any) // AuditHelper mock
		);
	});

	describe('getUserCollections', () => {
		it('should return user collections with enriched data', async () => {
			const userId = 'test-user-id';
			const mockCollections = [mockCollection()];

			mockCollectionRepository.findUserCollections.mockResolvedValue(mockCollections);
			mockWordService.findWordsByIds!.mockResolvedValue([]);

			const result = await collectionService.getUserCollections(userId);

			expect(mockCollectionRepository.findUserCollections).toHaveBeenCalledWith(userId);
			expect(result).toHaveLength(1);
			expect(result[0]).toMatchObject({
				...mockCollections[0],
				words: [],
				wordCount: 0,
			});
		});

		it('should handle repository errors', async () => {
			const userId = 'test-user-id';
			mockCollectionRepository.findUserCollections.mockRejectedValue(
				new Error('Database error')
			);

			await expect(collectionService.getUserCollections(userId)).rejects.toThrow(
				'Failed to retrieve collections'
			);
		});
	});

	describe('createCollection', () => {
		it('should create a new collection successfully', async () => {
			const userId = 'test-user-id';
			const name = 'Test Collection';
			const targetLanguage = Language.EN;
			const sourceLanguage = Language.VI;
			const mockCreatedCollection = mockCollection({
				name,
				target_language: targetLanguage,
				source_language: sourceLanguage,
			});

			mockCollectionRepository.create.mockResolvedValue(mockCreatedCollection);
			mockWordService.findWordsByIds!.mockResolvedValue([]);

			const result = await collectionService.createCollection(
				userId,
				name,
				targetLanguage,
				sourceLanguage
			);

			expect(mockCollectionRepository.create).toHaveBeenCalledWith({
				name,
				target_language: targetLanguage,
				source_language: sourceLanguage,
				user: { connect: { id: userId } },
			});
			expect(result).toMatchObject({
				...mockCreatedCollection,
				words: [],
				wordCount: 0,
			});
		});

		it('should create collection with initial words', async () => {
			const userId = 'test-user-id';
			const name = 'Test Collection';
			const targetLanguage = Language.EN;
			const sourceLanguage = Language.VI;
			const wordIds = ['word1', 'word2'];
			const mockWords = [mockWordDetail({ id: 'word1' }), mockWordDetail({ id: 'word2' })];
			const mockCreatedCollection = mockCollection({
				name,
				target_language: targetLanguage,
				source_language: sourceLanguage,
				word_ids: wordIds,
			});

			mockCollectionRepository.create.mockResolvedValue(mockCreatedCollection);
			mockWordService.findWordsByIds!.mockResolvedValue(mockWords);

			const result = await collectionService.createCollection(
				userId,
				name,
				targetLanguage,
				sourceLanguage,
				wordIds
			);

			expect(mockCollectionRepository.create).toHaveBeenCalledWith({
				name,
				target_language: targetLanguage,
				source_language: sourceLanguage,
				user: { connect: { id: userId } },
				word_ids: wordIds,
			});
			expect(result.words).toHaveLength(2);
			expect(result.wordCount).toBe(2);
		});

		it('should handle creation failure', async () => {
			const userId = 'test-user-id';
			const name = 'Test Collection';
			const targetLanguage = Language.EN;
			const sourceLanguage = Language.VI;

			mockCollectionRepository.create.mockResolvedValue(null as any);

			await expect(
				collectionService.createCollection(userId, name, targetLanguage, sourceLanguage)
			).rejects.toThrow('Failed to create collection');
		});
	});

	describe('updateCollection', () => {
		it('should update collection name successfully', async () => {
			const userId = 'test-user-id';
			const collectionId = 'collection-id';
			const newName = 'Updated Collection';
			const existingCollection = mockCollection({ id: collectionId, user_id: userId });
			const updatedCollection = mockCollection({ ...existingCollection, name: newName });

			mockCollectionRepository.findOne.mockResolvedValue(existingCollection);
			mockCollectionRepository.update.mockResolvedValue(updatedCollection);
			mockWordService.findWordsByIds!.mockResolvedValue([]);

			const result = await collectionService.updateCollection(userId, collectionId, newName);

			expect(mockCollectionRepository.findOne).toHaveBeenCalledWith({
				id: collectionId,
				user_id: userId,
			});
			expect(mockCollectionRepository.update).toHaveBeenCalledWith(collectionId, {
				name: newName,
			});
			expect(result?.name).toBe(newName);
		});

		it('should return null for non-existent collection', async () => {
			const userId = 'test-user-id';
			const collectionId = 'non-existent-id';

			mockCollectionRepository.findOne.mockResolvedValue(null);

			const result = await collectionService.updateCollection(
				userId,
				collectionId,
				'New Name'
			);

			expect(result).toBeNull();
		});

		it('should return null for unauthorized access', async () => {
			const userId = 'test-user-id';
			const collectionId = 'collection-id';
			mockCollectionRepository.findOne.mockResolvedValue(null); // findOne with user_id filter returns null

			const result = await collectionService.updateCollection(
				userId,
				collectionId,
				'New Name'
			);

			expect(result).toBeNull();
		});
	});

	describe('deleteCollection', () => {
		it('should delete collection successfully', async () => {
			const userId = 'test-user-id';
			const collectionId = 'collection-id';
			const existingCollection = mockCollection({ id: collectionId, user_id: userId });

			mockCollectionRepository.findOne.mockResolvedValue(existingCollection);
			mockCollectionRepository.delete.mockResolvedValue(true);

			const result = await collectionService.deleteCollection(userId, collectionId);

			expect(mockCollectionRepository.findOne).toHaveBeenCalledWith({
				id: collectionId,
				user_id: userId,
			});
			expect(mockCollectionRepository.delete).toHaveBeenCalledWith({ id: collectionId });
			expect(result).toBe(true);
		});

		it('should return false for non-existent collection', async () => {
			const userId = 'test-user-id';
			const collectionId = 'non-existent-id';

			mockCollectionRepository.findOne.mockResolvedValue(null);

			const result = await collectionService.deleteCollection(userId, collectionId);

			expect(result).toBe(false);
		});

		it('should return false for unauthorized access', async () => {
			const userId = 'test-user-id';
			const collectionId = 'collection-id';
			mockCollectionRepository.findOne.mockResolvedValue(null); // findOne with user_id filter returns null

			const result = await collectionService.deleteCollection(userId, collectionId);

			expect(result).toBe(false);
		});
	});

	describe('addWordsToCollection', () => {
		it('should add words to collection successfully', async () => {
			const userId = 'test-user-id';
			const collectionId = 'collection-id';
			const wordIds = ['word1', 'word2'];
			const existingCollection = mockCollection({
				id: collectionId,
				user_id: userId,
				word_ids: [],
			});
			const updatedCollection = mockCollection({ ...existingCollection, word_ids: wordIds });
			const mockWords = [mockWordDetail({ id: 'word1' }), mockWordDetail({ id: 'word2' })];

			mockCollectionRepository.findOne.mockResolvedValue(existingCollection);
			mockCollectionRepository.update.mockResolvedValue(updatedCollection);
			mockWordService.findWordsByIds!.mockResolvedValue(mockWords);

			const result = await collectionService.addWordsToCollection(
				userId,
				collectionId,
				wordIds
			);

			expect(mockCollectionRepository.findOne).toHaveBeenCalledWith({
				id: collectionId,
				user_id: userId,
			});
			expect(mockCollectionRepository.update).toHaveBeenCalledWith(collectionId, {
				word_ids: wordIds,
			});
			expect(result?.words).toHaveLength(2);
		});

		it('should handle duplicate words', async () => {
			const userId = 'test-user-id';
			const collectionId = 'collection-id';
			const wordIds = ['word1', 'word2'];
			const existingCollection = mockCollection({
				id: collectionId,
				user_id: userId,
				word_ids: ['word1'], // word1 already exists
			});

			mockCollectionRepository.findOne.mockResolvedValue(existingCollection);
			mockCollectionRepository.update.mockResolvedValue(existingCollection);

			await collectionService.addWordsToCollection(userId, collectionId, wordIds);

			// Should merge word_ids (word1 already exists, word2 is new)
			expect(mockCollectionRepository.update).toHaveBeenCalledWith(collectionId, {
				word_ids: ['word1', 'word2'], // Merged and deduplicated
			});
		});
	});
});
