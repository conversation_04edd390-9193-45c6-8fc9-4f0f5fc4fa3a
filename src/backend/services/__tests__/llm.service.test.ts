import { jest, describe, beforeEach, it, expect } from '@jest/globals';

// Mock OpenAI
const mockOpenAI = {
	chat: {
		completions: {
			create: jest.fn(),
		},
	},
};

jest.mock('openai', () => ({
	__esModule: true,
	default: jest.fn().mockImplementation(() => mockOpenAI),
}));

// Mock config
jest.mock('@/config', () => ({
	getLLMConfig: jest.fn().mockResolvedValue({
		openAIKey: 'test-openai-key',
		openAIModel: 'gpt-4o-mini',
		geminiKey: 'test-gemini-key',
		geminiModel: 'gemini-1.5-flash',
		defaultProvider: 'openai',
		maxExamples: 8,
		temperature: 0.7,
		maxTokens: 1000,
	}),
	getLLMOptimizationConfig: jest.fn().mockResolvedValue({
		enabled: false,
		promptOptimization: { enabled: false, compressionTarget: 0.4 },
		caching: { enabled: false },
		batchProcessing: { enabled: false },
		modelSelection: { enabled: false },
		tokenManagement: { budgetLimits: { daily: 100000, monthly: 2500000 } },
		monitoring: { enabled: false },
	}),
}));

// Mock cache services
jest.mock('../semantic-cache.service', () => ({
	semanticCache: {
		getWithSemantic: jest.fn(),
		setWithSemantic: jest.fn(),
		generateLLMKey: jest.fn(),
	},
}));

jest.mock('../token-monitor.service', () => ({
	tokenMonitor: {
		trackUsage: jest.fn(),
		checkBudget: jest.fn().mockResolvedValue(true),
	},
}));

// Import after mocks
import {
	LLMService,
	GrammarPracticeParams,
	GrammarPracticeResultItem,
	GenerateParagraphParams,
	GenerateParagraphWithQuestionsParams,
	EvaluateAnswersParams,
} from '../llm.service';
import { Language, Difficulty } from '@prisma/client';
import { WordService } from '../word.service';
import {
	mockWordDetail,
	mockGrammarPracticeParams,
	mockGrammarPracticeResult,
} from '../../../test/fixtures';

// Mock dependencies
const mockWordService = {
	findByIds: jest.fn(),
	findByTerms: jest.fn(),
	getWordsByTerms: jest.fn(),
	createWords: jest.fn(),
	createWordWithRandomWordDetail: jest.fn(),
	searchWords: jest.fn(),
} as jest.Mocked<Partial<WordService>>;

describe('LLMService', () => {
	let llmService: LLMService;

	beforeEach(() => {
		jest.clearAllMocks();
		// Setup default mock returns
		(mockWordService.getWordsByTerms as any).mockResolvedValue([]);
		(mockWordService.createWordWithRandomWordDetail as any).mockResolvedValue({
			id: 1,
			term: 'test',
			language: 'EN',
			createdAt: new Date(),
			updatedAt: new Date(),
		});
		llmService = new LLMService(() => mockWordService as WordService);
	});

	describe('generateWordDetails', () => {
		it('should generate word details successfully', async () => {
			const terms = ['test', 'example'];
			const sourceLanguage = Language.VI;
			const targetLanguage = Language.EN;
			const mockResponse = {
				choices: [
					{
						message: {
							content: JSON.stringify({
								words: [
									{
										term: 'test',
										language: 'EN',
										definitions: [
											{
												pos: ['noun'],
												ipa: '/test/',
												explains: { EN: 'A test', VI: 'Một bài kiểm tra' },
												examples: {
													EN: 'This is a test',
													VI: 'Đây là một bài kiểm tra',
												},
											},
										],
									},
								],
							}),
						},
					},
				],
				usage: { total_tokens: 100 },
			};

			(mockOpenAI.chat.completions.create as any).mockResolvedValue(mockResponse);

			const result = await llmService.generateWordDetails(
				terms,
				sourceLanguage,
				targetLanguage
			);

			expect(mockOpenAI.chat.completions.create).toHaveBeenCalled();
			expect(result).toHaveLength(1);
			expect(result[0].term).toBe('test');
			expect(result[0].language).toBe(Language.EN);
		});

		it('should handle API errors', async () => {
			const terms = ['test'];
			const sourceLanguage = Language.VI;
			const targetLanguage = Language.EN;

			(mockOpenAI.chat.completions.create as any).mockRejectedValueOnce(
				new Error('API Error')
			);

			await expect(
				llmService.generateWordDetails(terms, sourceLanguage, targetLanguage)
			).rejects.toThrow('API Error');

			expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(1);
		});

		it('should throw error on API failure', async () => {
			const terms = ['test'];
			const sourceLanguage = Language.VI;
			const targetLanguage = Language.EN;

			(mockOpenAI.chat.completions.create as any).mockRejectedValue(
				new Error('Persistent API Error')
			);

			await expect(
				llmService.generateWordDetails(terms, sourceLanguage, targetLanguage)
			).rejects.toThrow('Persistent API Error');
		});
	});

	describe('generateParagraph', () => {
		it('should generate paragraphs successfully', async () => {
			const params: GenerateParagraphParams = {
				keywords: ['technology', 'innovation'],
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				count: 2,
				sentenceCount: 5,
			};

			const mockResponse = {
				choices: [
					{
						message: {
							content: JSON.stringify({
								paragraphs: [
									'Technology drives innovation in modern society.',
									'Innovation creates new opportunities for technological advancement.',
								],
							}),
						},
					},
				],
				usage: { total_tokens: 150 },
			};

			(mockOpenAI.chat.completions.create as any).mockResolvedValue(mockResponse);

			const result = await llmService.generateParagraph(params);

			expect(mockOpenAI.chat.completions.create).toHaveBeenCalled();
			expect(result).toHaveLength(2);
			expect(result[0]).toContain('Technology');
			expect(result[1]).toContain('Innovation');
		});

		it('should validate input parameters', async () => {
			const invalidParams: GenerateParagraphParams = {
				keywords: [],
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				count: 0,
			};

			await expect(llmService.generateParagraph(invalidParams)).rejects.toThrow();
		});
	});

	describe('generateParagraphWithQuestions', () => {
		it('should generate paragraph with questions successfully', async () => {
			const params: GenerateParagraphWithQuestionsParams = {
				keywords: ['science', 'research'],
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				sentenceCount: 10,
				questionCount: 3,
			};

			const mockResponse = {
				choices: [
					{
						message: {
							content: JSON.stringify({
								paragraph:
									'Science and research are fundamental to human progress.',
								questions: [
									'What is fundamental to human progress?',
									'How does science contribute to society?',
									'Why is research important?',
								],
							}),
						},
					},
				],
				usage: { total_tokens: 200 },
			};

			(mockOpenAI.chat.completions.create as any).mockResolvedValue(mockResponse);

			const result = await llmService.generateParagraphWithQuestions(params);

			expect(mockOpenAI.chat.completions.create).toHaveBeenCalled();
			expect(result.paragraph).toContain('Science');
			expect(result.questions).toHaveLength(3);
			expect(result.questions[0]).toContain('progress');
		});
	});

	describe('evaluateAnswers', () => {
		it('should evaluate answers successfully', async () => {
			const params: EvaluateAnswersParams = {
				paragraph: 'Science is important for progress.',
				questions: ['What is important for progress?'],
				answers: ['Science'],
				qna_language: Language.EN,
				feedback_native_language: Language.VI,
			};

			const mockResponse = {
				choices: [
					{
						message: {
							content: JSON.stringify({
								evaluations: [
									{
										score: 10,
										feedback: {
											qna_feedback_text: 'Correct answer',
											native_feedback_text: 'Câu trả lời chính xác',
										},
										is_correct: true,
										suggested_answer: null,
									},
								],
							}),
						},
					},
				],
				usage: { total_tokens: 120 },
			};

			(mockOpenAI.chat.completions.create as any).mockResolvedValue(mockResponse);

			const result = await llmService.evaluateAnswers(params);

			expect(mockOpenAI.chat.completions.create).toHaveBeenCalled();
			expect(result).toHaveLength(1);
			expect(result[0].score).toBe(10);
			expect(result[0].feedback.qna_feedback_text).toBe('Correct answer');
		});
	});

	describe('generateGrammarPractice', () => {
		it('should generate grammar practice successfully', async () => {
			const params = mockGrammarPracticeParams({ count: 1 }); // Only request 1 exercise
			const mockResult = mockGrammarPracticeResult();

			const mockResponse = {
				choices: [
					{
						message: {
							content: JSON.stringify({
								paragraphs: [mockResult],
							}),
						},
					},
				],
				usage: { total_tokens: 180 },
			};

			(mockOpenAI.chat.completions.create as any).mockResolvedValue(mockResponse);

			const result = await llmService.generateGrammarPractice(params);

			expect(mockOpenAI.chat.completions.create).toHaveBeenCalled();
			expect(result).toHaveLength(1);
			expect(result[0]).toMatchObject(mockResult);
		});

		it('should handle different error densities', async () => {
			const params = mockGrammarPracticeParams({ errorDensity: 'high', count: 1 });

			const mockResponse = {
				choices: [
					{
						message: {
							content: JSON.stringify({
								paragraphs: [mockGrammarPracticeResult()],
							}),
						},
					},
				],
				usage: { total_tokens: 180 },
			};

			(mockOpenAI.chat.completions.create as any).mockResolvedValue(mockResponse);

			const result = await llmService.generateGrammarPractice(params);

			expect(result).toHaveLength(1);
			// Verify that the prompt includes error density instructions
			const callArgs = mockOpenAI.chat.completions.create.mock.calls[0][0];
			expect((callArgs as any).messages[0].content).toContain('exactly 5 errors');
		});
	});
});
