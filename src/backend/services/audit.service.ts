import { UserRepository } from '@/backend/repositories';
import { AuditRepository } from '@/backend/repositories/audit.repository';
import { Prisma } from '@prisma/client';

export interface AuditEvent {
	id: string;
	action: string;
	resource: string;
	resource_id?: string;
	user_id?: string;
	admin_id?: string;
	details: Record<string, any>;
	ip_address?: string;
	user_agent?: string;
	timestamp: Date;
}

export interface AuditLogQuery {
	action?: string;
	resource?: string;
	user_id?: string;
	admin_id?: string;
	start_date?: Date;
	end_date?: Date;
	limit?: number;
	offset?: number;
}

export interface AuditService {
	logEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): Promise<void>;
	getAuditLogs(query: AuditLogQuery): Promise<AuditEvent[]>;
	getAuditLogsCount(query: Omit<AuditLogQuery, 'limit' | 'offset'>): Promise<number>;
	getUserActivity(userId: string, limit?: number): Promise<AuditEvent[]>;
	getAdminActivity(adminId: string, limit?: number): Promise<AuditEvent[]>;
	getSystemActivity(limit?: number): Promise<AuditEvent[]>;
}

export class AuditServiceImpl implements AuditService {
	constructor(
		private readonly getUserRepository: () => UserRepository,
		private readonly getAuditRepository: () => AuditRepository
	) {}

	async logEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): Promise<void> {
		try {
			const auditRepository = this.getAuditRepository();

			// Convert details to JSON if it's an object
			const details = typeof event.details === 'object' ? event.details : {};

			const auditData: Prisma.AuditLogCreateInput = {
				action: event.action,
				resource: event.resource,
				resource_id: event.resource_id,
				details: details,
				ip_address: event.ip_address,
				user_agent: event.user_agent,
				user: event.user_id ? { connect: { id: event.user_id } } : undefined,
				admin: event.admin_id ? { connect: { id: event.admin_id } } : undefined,
			};

			const auditLog = await auditRepository.create(auditData);
			console.log('Audit Event logged:', auditLog.id);
		} catch (error) {
			console.error('Failed to log audit event:', error);
			// Don't throw error to prevent breaking the main operation
		}
	}

	async getAuditLogs(query: AuditLogQuery): Promise<AuditEvent[]> {
		try {
			const auditRepository = this.getAuditRepository();
			const auditLogs = await auditRepository.findAuditLogs(query);

			// Convert Prisma AuditLog to AuditEvent format
			return auditLogs.map((log) => ({
				id: log.id,
				action: log.action,
				resource: log.resource,
				resource_id: log.resource_id || undefined,
				user_id: log.user_id || undefined,
				admin_id: log.admin_id || undefined,
				details:
					typeof log.details === 'object' ? (log.details as Record<string, any>) : {},
				ip_address: log.ip_address || undefined,
				user_agent: log.user_agent || undefined,
				timestamp: log.timestamp,
			}));
		} catch (error) {
			console.error('Failed to get audit logs:', error);
			return [];
		}
	}

	async getAuditLogsCount(query: Omit<AuditLogQuery, 'limit' | 'offset'>): Promise<number> {
		try {
			const auditRepository = this.getAuditRepository();
			return await auditRepository.countAuditLogs(query);
		} catch (error) {
			console.error('Failed to count audit logs:', error);
			return 0;
		}
	}

	async getUserActivity(userId: string, limit: number = 50): Promise<AuditEvent[]> {
		return this.getAuditLogs({ user_id: userId, limit });
	}

	async getAdminActivity(adminId: string, limit: number = 50): Promise<AuditEvent[]> {
		return this.getAuditLogs({ admin_id: adminId, limit });
	}

	async getSystemActivity(limit: number = 50): Promise<AuditEvent[]> {
		return this.getAuditLogs({ resource: 'system', limit });
	}
}

// Audit event types
export const AUDIT_ACTIONS = {
	// Authentication actions
	LOGIN: 'login',
	LOGOUT: 'logout',
	LOGIN_FAILED: 'login_failed',
	REGISTER: 'register',
	PASSWORD_CHANGED: 'password_changed',
	PASSWORD_RESET: 'password_reset',

	// User actions
	USER_CREATED: 'user_created',
	USER_UPDATED: 'user_updated',
	USER_DISABLED: 'user_disabled',
	USER_ENABLED: 'user_enabled',
	USER_ROLE_CHANGED: 'user_role_changed',
	USER_DELETED: 'user_deleted',
	USER_PROFILE_UPDATED: 'user_profile_updated',
	USER_SETTINGS_UPDATED: 'user_settings_updated',

	// Admin actions
	ADMIN_LOGIN: 'admin_login',
	ADMIN_LOGOUT: 'admin_logout',
	ADMIN_USER_CREATED: 'admin_user_created',
	ADMIN_USER_UPDATED: 'admin_user_updated',
	ADMIN_USER_DISABLED: 'admin_user_disabled',
	ADMIN_USER_ENABLED: 'admin_user_enabled',
	ADMIN_USER_DELETED: 'admin_user_deleted',
	ADMIN_COLLECTION_DELETED: 'admin_collection_deleted',
	ADMIN_CACHE_CLEARED: 'admin_cache_cleared',
	ADMIN_SYSTEM_CONFIG_CHANGED: 'admin_system_config_changed',

	// Collection actions
	COLLECTION_CREATED: 'collection_created',
	COLLECTION_UPDATED: 'collection_updated',
	COLLECTION_DELETED: 'collection_deleted',
	COLLECTION_WORDS_ADDED: 'collection_words_added',
	COLLECTION_WORDS_REMOVED: 'collection_words_removed',
	COLLECTION_TERMS_ADDED: 'collection_terms_added',
	COLLECTION_ACCESSED: 'collection_accessed',
	COLLECTION_SHARED: 'collection_shared',

	// Word actions
	WORD_CREATED: 'word_created',
	WORD_UPDATED: 'word_updated',
	WORD_DELETED: 'word_deleted',
	WORD_SEARCHED: 'word_searched',
	WORD_REVIEWED: 'word_reviewed',
	WORD_EXAMPLES_ADDED: 'word_examples_added',

	// Feedback actions
	FEEDBACK_CREATED: 'feedback_created',
	FEEDBACK_UPDATED: 'feedback_updated',
	FEEDBACK_DELETED: 'feedback_deleted',
	FEEDBACK_STATUS_UPDATED: 'feedback_status_updated',
	FEEDBACK_REVIEWED: 'feedback_reviewed',

	// Practice actions
	PRACTICE_SESSION_STARTED: 'practice_session_started',
	PRACTICE_SESSION_COMPLETED: 'practice_session_completed',
	QA_PRACTICE_SUBMITTED: 'qa_practice_submitted',
	PARAGRAPH_PRACTICE_SUBMITTED: 'paragraph_practice_submitted',

	// LLM actions
	LLM_REQUEST: 'llm_request',
	LLM_RESPONSE: 'llm_response',
	LLM_ERROR: 'llm_error',
	LLM_QUOTA_EXCEEDED: 'llm_quota_exceeded',

	// Cache actions
	CACHE_CLEARED: 'cache_cleared',
	CACHE_HIT: 'cache_hit',
	CACHE_MISS: 'cache_miss',
	CACHE_SET: 'cache_set',
	CACHE_DELETE: 'cache_delete',

	// System actions
	SYSTEM_ERROR: 'system_error',
	SYSTEM_WARNING: 'system_warning',
	SYSTEM_INFO: 'system_info',
	SYSTEM_STARTUP: 'system_startup',
	SYSTEM_SHUTDOWN: 'system_shutdown',
	DATABASE_MIGRATION: 'database_migration',

	// Security actions
	UNAUTHORIZED_ACCESS: 'unauthorized_access',
	PERMISSION_DENIED: 'permission_denied',
	SUSPICIOUS_ACTIVITY: 'suspicious_activity',
	RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',

	// Data actions
	DATA_EXPORT: 'data_export',
	DATA_IMPORT: 'data_import',
	DATA_BACKUP: 'data_backup',
	DATA_RESTORE: 'data_restore',
} as const;

export const AUDIT_RESOURCES = {
	USER: 'user',
	ADMIN: 'admin',
	COLLECTION: 'collection',
	WORD: 'word',
	FEEDBACK: 'feedback',
	SYSTEM: 'system',
	CACHE: 'cache',
	AUTH: 'auth',
	SESSION: 'session',
	PRACTICE: 'practice',
	LLM: 'llm',
	DATABASE: 'database',
	API: 'api',
	SECURITY: 'security',
	DATA: 'data',
} as const;

// Helper function to create audit events
export function createAuditEvent(
	action: string,
	resource: string,
	details: Record<string, any> = {},
	options: {
		resource_id?: string;
		user_id?: string;
		admin_id?: string;
		ip_address?: string;
		user_agent?: string;
	} = {}
): Omit<AuditEvent, 'id' | 'timestamp'> {
	return {
		action,
		resource,
		details,
		...options,
	};
}
