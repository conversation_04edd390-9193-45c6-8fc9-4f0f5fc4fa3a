import * as natural from 'natural';
import { Language } from '@prisma/client';
import { getWordRepository } from '../wire';

export interface WordNetInfo {
	synsets: string[];
	lemma: string | null;
	hypernyms: string[];
	hyponyms: string[];
	holonyms: string[];
	meronyms: string[];
}

export interface WordNetService {
	getWordNetInfo(term: string, language: Language): Promise<WordNetInfo>;
	getWordNetInfoFromDatabase(term: string, language: Language): Promise<WordNetInfo | null>;
	isWordNetAvailable(): boolean;
}

export class WordNetServiceImpl implements WordNetService {
	private wordNet: any;
	private isInitialized = false;

	constructor() {
		try {
			this.wordNet = new natural.WordNet();
		} catch (error) {
			console.error('Failed to initialize WordNet:', error);
			this.wordNet = null;
		}
	}

	/**
	 * Initialize WordNet if not already initialized
	 */
	private async ensureInitialized(): Promise<void> {
		if (this.isInitialized) return;

		try {
			// WordNet is initialized in constructor
			this.isInitialized = true;
		} catch (error) {
			console.error('Failed to initialize WordNet:', error);
			throw new Error('WordNet initialization failed');
		}
	}

	/**
	 * Check if WordNet is available
	 */
	isWordNetAvailable(): boolean {
		return !!this.wordNet;
	}

	/**
	 * Get WordNet information for a term (database-first approach)
	 */
	async getWordNetInfo(term: string, language: Language): Promise<WordNetInfo> {
		// WordNet is primarily for English, so we only process English terms
		if (language !== Language.EN) {
			return this.getEmptyWordNetInfo();
		}

		// Try to get from database first
		const dbResult = await this.getWordNetInfoFromDatabase(term, language);
		if (dbResult) {
			return dbResult;
		}

		// Fallback to real-time lookup
		await this.ensureInitialized();

		if (!this.isWordNetAvailable()) {
			console.warn('WordNet is not available');
			return this.getEmptyWordNetInfo();
		}

		try {
			const wordNetInfo = await this.extractWordNetData(term);
			return wordNetInfo;
		} catch (error) {
			console.error(`Error getting WordNet info for term "${term}":`, error);
			return this.getEmptyWordNetInfo();
		}
	}

	/**
	 * Get WordNet information from database
	 */
	async getWordNetInfoFromDatabase(
		term: string,
		language: Language
	): Promise<WordNetInfo | null> {
		if (language !== Language.EN) {
			return null;
		}

		try {
			const wordRepository = getWordRepository();
			const word = await wordRepository.findOne({
				term: term.toLowerCase(),
				language: Language.EN,
			});

			if (!word || !word.wordnet_data) {
				return null;
			}

			return {
				synsets: word.wordnet_data.synsets || [],
				lemma: word.wordnet_data.lemma,
				hypernyms: word.wordnet_data.hypernyms || [],
				hyponyms: word.wordnet_data.hyponyms || [],
				holonyms: word.wordnet_data.holonyms || [],
				meronyms: word.wordnet_data.meronyms || [],
			};
		} catch (error) {
			console.error(`Error getting WordNet info from database for term "${term}":`, error);
			return null;
		}
	}

	/**
	 * Extract WordNet data for a term
	 */
	private async extractWordNetData(term: string): Promise<WordNetInfo> {
		return new Promise((resolve) => {
			const result: WordNetInfo = {
				synsets: [],
				lemma: null,
				hypernyms: [],
				hyponyms: [],
				holonyms: [],
				meronyms: [],
			};

			try {
				// Get synsets for the term
				this.wordNet.lookup(term, (results: any[]) => {
					if (!results || results.length === 0) {
						resolve(result);
						return;
					}

					// Set lemma (base form)
					result.lemma = term.toLowerCase();

					// Process each synset
					results.forEach((synset: any) => {
						// Add synset definition (gloss)
						if (synset.gloss) {
							result.synsets.push(synset.gloss);
						}

						// Add synonyms as related terms
						if (synset.synonyms && Array.isArray(synset.synonyms)) {
							synset.synonyms.forEach((synonym: string) => {
								if (synonym !== term && !result.hypernyms.includes(synonym)) {
									result.hypernyms.push(synonym);
								}
							});
						}

						// For now, we'll use synonyms as hypernyms since natural's WordNet
						// doesn't directly expose hypernyms/hyponyms in the basic API
						// In a more advanced implementation, we could use the synset offset
						// to look up related synsets
					});

					resolve(result);
				});
			} catch (error) {
				console.error('Error in WordNet lookup:', error);
				resolve(result);
			}
		});
	}

	/**
	 * Get empty WordNet info structure
	 */
	private getEmptyWordNetInfo(): WordNetInfo {
		return {
			synsets: [],
			lemma: null,
			hypernyms: [],
			hyponyms: [],
			holonyms: [],
			meronyms: [],
		};
	}
}
