import { CollectionRepository, CollectionRepositoryImpl } from '../collection.repository';
import { PrismaClient, Language } from '@prisma/client';
import { mockCollection } from '@/test/fixtures';

// Mock Prisma Client
const mockPrisma = {
	collection: {
		findMany: jest.fn(),
		findUnique: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
		delete: jest.fn(),
	},
} as jest.Mocked<Partial<PrismaClient>>;

describe('CollectionRepository', () => {
	let collectionRepository: CollectionRepository;

	beforeEach(() => {
		jest.clearAllMocks();
		collectionRepository = new CollectionRepositoryImpl(mockPrisma as PrismaClient);
	});

	describe('findById', () => {
		it('should find collection by id', async () => {
			const collectionId = 'test-collection-id';
			const mockCollectionData = mockCollection({ id: collectionId });

			mockPrisma.collection!.findUnique!.mockResolvedValue(mockCollectionData);

			const result = await collectionRepository.findById(collectionId);

			expect(mockPrisma.collection!.findUnique).toHaveBeenCalledWith({
				where: { id: collectionId },
			});
			expect(result).toEqual(mockCollectionData);
		});

		it('should return null for non-existent collection', async () => {
			const collectionId = 'non-existent-id';

			mockPrisma.collection!.findUnique!.mockResolvedValue(null);

			const result = await collectionRepository.findById(collectionId);

			expect(result).toBeNull();
		});
	});

	describe('findUserCollections', () => {
		it('should find collections for a user', async () => {
			const userId = 'test-user-id';
			const mockCollections = [
				mockCollection({ user_id: userId }),
				mockCollection({ user_id: userId }),
			];

			mockPrisma.collection!.findMany!.mockResolvedValue(mockCollections);

			const result = await collectionRepository.findUserCollections(userId);

			expect(mockPrisma.collection!.findMany).toHaveBeenCalledWith({
				where: { user_id: userId },
				orderBy: { created_at: 'desc' },
			});
			expect(result).toEqual(mockCollections);
		});

		it('should return empty array for user with no collections', async () => {
			const userId = 'user-with-no-collections';

			mockPrisma.collection!.findMany!.mockResolvedValue([]);

			const result = await collectionRepository.findUserCollections(userId);

			expect(result).toEqual([]);
		});
	});

	describe('findByUserIdAndName', () => {
		it('should find collection by user id and name', async () => {
			const userId = 'test-user-id';
			const name = 'Test Collection';
			const mockCollectionData = mockCollection({ user_id: userId, name });

			mockPrisma.collection!.findUnique!.mockResolvedValue(mockCollectionData);

			const result = await collectionRepository.findByUserIdAndName(userId, name);

			expect(mockPrisma.collection!.findUnique).toHaveBeenCalledWith({
				where: {
					user_id_name: {
						user_id: userId,
						name: name,
					},
				},
			});
			expect(result).toEqual(mockCollectionData);
		});

		it('should return null for non-existent collection', async () => {
			const userId = 'test-user-id';
			const name = 'Non-existent Collection';

			mockPrisma.collection!.findUnique!.mockResolvedValue(null);

			const result = await collectionRepository.findByUserIdAndName(userId, name);

			expect(result).toBeNull();
		});
	});

	describe('create', () => {
		it('should create a new collection', async () => {
			const createData = {
				name: 'New Collection',
				target_language: Language.EN,
				source_language: Language.VI,
				user: { connect: { id: 'test-user-id' } },
			};
			const mockCreatedCollection = mockCollection(createData);

			mockPrisma.collection!.create!.mockResolvedValue(mockCreatedCollection);

			const result = await collectionRepository.create(createData);

			expect(mockPrisma.collection!.create).toHaveBeenCalledWith({
				data: createData,
			});
			expect(result).toEqual(mockCreatedCollection);
		});

		it('should handle creation errors', async () => {
			const createData = {
				name: 'New Collection',
				target_language: Language.EN,
				source_language: Language.VI,
				user: { connect: { id: 'test-user-id' } },
			};

			mockPrisma.collection!.create!.mockRejectedValue(new Error('Database error'));

			await expect(collectionRepository.create(createData)).rejects.toThrow('Database error');
		});
	});

	describe('update', () => {
		it('should update collection', async () => {
			const collectionId = 'test-collection-id';
			const updateData = { name: 'Updated Collection' };
			const mockUpdatedCollection = mockCollection({ id: collectionId, ...updateData });

			mockPrisma.collection!.update!.mockResolvedValue(mockUpdatedCollection);

			const result = await collectionRepository.update(collectionId, updateData);

			expect(mockPrisma.collection!.update).toHaveBeenCalledWith({
				where: { id: collectionId },
				data: updateData,
			});
			expect(result).toEqual(mockUpdatedCollection);
		});

		it('should handle update errors', async () => {
			const collectionId = 'test-collection-id';
			const updateData = { name: 'Updated Collection' };

			mockPrisma.collection!.update!.mockRejectedValue(new Error('Update failed'));

			await expect(collectionRepository.update(collectionId, updateData)).rejects.toThrow('Update failed');
		});
	});

	describe('delete', () => {
		it('should delete collection', async () => {
			const collectionId = 'test-collection-id';

			mockPrisma.collection!.delete!.mockResolvedValue(mockCollection({ id: collectionId }));

			const result = await collectionRepository.delete(collectionId);

			expect(mockPrisma.collection!.delete).toHaveBeenCalledWith({
				where: { id: collectionId },
			});
			expect(result).toBe(true);
		});

		it('should handle deletion errors', async () => {
			const collectionId = 'test-collection-id';

			mockPrisma.collection!.delete!.mockRejectedValue(new Error('Deletion failed'));

			await expect(collectionRepository.delete(collectionId)).rejects.toThrow('Deletion failed');
		});
	});

	describe('addWordsToCollection', () => {
		it('should add words to collection', async () => {
			const collectionId = 'test-collection-id';
			const wordIds = ['word1', 'word2'];
			const existingCollection = mockCollection({ id: collectionId, word_ids: ['existing-word'] });
			const updatedCollection = mockCollection({ 
				id: collectionId, 
				word_ids: ['existing-word', ...wordIds] 
			});

			mockPrisma.collection!.findUnique!.mockResolvedValue(existingCollection);
			mockPrisma.collection!.update!.mockResolvedValue(updatedCollection);

			const result = await collectionRepository.addWordsToCollection(collectionId, wordIds);

			expect(mockPrisma.collection!.findUnique).toHaveBeenCalledWith({
				where: { id: collectionId },
			});
			expect(mockPrisma.collection!.update).toHaveBeenCalledWith({
				where: { id: collectionId },
				data: {
					word_ids: ['existing-word', ...wordIds],
				},
			});
			expect(result).toEqual(updatedCollection);
		});

		it('should return null for non-existent collection', async () => {
			const collectionId = 'non-existent-id';
			const wordIds = ['word1', 'word2'];

			mockPrisma.collection!.findUnique!.mockResolvedValue(null);

			const result = await collectionRepository.addWordsToCollection(collectionId, wordIds);

			expect(result).toBeNull();
		});
	});

	describe('removeWordsFromCollection', () => {
		it('should remove words from collection', async () => {
			const collectionId = 'test-collection-id';
			const wordIdsToRemove = ['word1', 'word2'];
			const existingCollection = mockCollection({ 
				id: collectionId, 
				word_ids: ['word1', 'word2', 'word3'] 
			});
			const updatedCollection = mockCollection({ 
				id: collectionId, 
				word_ids: ['word3'] 
			});

			mockPrisma.collection!.findUnique!.mockResolvedValue(existingCollection);
			mockPrisma.collection!.update!.mockResolvedValue(updatedCollection);

			const result = await collectionRepository.removeWordsFromCollection(collectionId, wordIdsToRemove);

			expect(mockPrisma.collection!.findUnique).toHaveBeenCalledWith({
				where: { id: collectionId },
			});
			expect(mockPrisma.collection!.update).toHaveBeenCalledWith({
				where: { id: collectionId },
				data: {
					word_ids: ['word3'],
				},
			});
			expect(result).toEqual(updatedCollection);
		});

		it('should return null for non-existent collection', async () => {
			const collectionId = 'non-existent-id';
			const wordIds = ['word1', 'word2'];

			mockPrisma.collection!.findUnique!.mockResolvedValue(null);

			const result = await collectionRepository.removeWordsFromCollection(collectionId, wordIds);

			expect(result).toBeNull();
		});
	});
});
