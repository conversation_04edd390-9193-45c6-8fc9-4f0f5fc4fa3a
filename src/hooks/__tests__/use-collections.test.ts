import { renderHook, act, waitFor } from '@testing-library/react';
import { useCollections } from '../use-collections';
import { CollectionsContext } from '@/contexts';
import { Language } from '@prisma/client';
import { mockCollection, mockWordDetail } from '@/test/fixtures';
import React from 'react';

// Mock fetch
global.fetch = jest.fn();

// Mock IndexedDB functions
jest.mock('@/lib/indexed-db', () => ({
	getAllCollections: jest.fn(),
	saveCollection: jest.fn(),
	saveCollections: jest.fn(),
}));

// Mock error management
jest.mock('@/lib/error-management', () => ({
	retryApiCall: jest.fn((fn) => fn()),
	normalizeError: jest.fn((error) => error),
	createErrorContext: jest.fn(() => ({})),
}));

const mockContextValue = {
	collections: [],
	setCollections: jest.fn(),
	currentCollection: null,
	setCurrentCollection: jest.fn(),
	currentCollectionWords: [],
	setCurrentCollectionWords: jest.fn(),
	isLoading: false,
	setIsLoading: jest.fn(),
	error: null,
	setError: jest.fn(),
	fetchCollections: jest.fn(),
	createCollection: jest.fn(),
	updateCollection: jest.fn(),
	deleteCollection: jest.fn(),
	addWordsToCollection: jest.fn(),
	addTermToCollection: jest.fn(),
	removeWordsFromCollection: jest.fn(),
	fetchWordsByCollection: jest.fn(),
	clearCurrentCollection: jest.fn(),
	addTermToCurrentCollection: jest.fn(),
	addWordsToCurrentCollection: jest.fn(),
};

const wrapper = ({ children }: { children: React.ReactNode }) => (
	<CollectionsContext.Provider value={mockContextValue}>
		{children}
	</CollectionsContext.Provider>
);

describe('useCollections', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		(fetch as jest.Mock).mockClear();
	});

	it('should return collections context', () => {
		const { result } = renderHook(() => useCollections(), { wrapper });

		expect(result.current).toEqual(mockContextValue);
	});

	it('should throw error when used outside provider', () => {
		// Suppress console.error for this test
		const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

		expect(() => {
			renderHook(() => useCollections());
		}).toThrow('useCollections must be used within a CollectionsProvider');

		consoleSpy.mockRestore();
	});

	describe('fetchCollections', () => {
		it('should fetch collections successfully', async () => {
			const mockCollections = [mockCollection(), mockCollection()];
			(fetch as jest.Mock).mockResolvedValueOnce({
				ok: true,
				json: async () => mockCollections,
			});

			const mockFetchCollections = jest.fn().mockImplementation(async () => {
				const response = await fetch('/api/collections');
				const data = await response.json();
				mockContextValue.setCollections(data);
			});

			mockContextValue.fetchCollections = mockFetchCollections;

			const { result } = renderHook(() => useCollections(), { wrapper });

			await act(async () => {
				await result.current.fetchCollections();
			});

			expect(fetch).toHaveBeenCalledWith('/api/collections');
			expect(mockContextValue.setCollections).toHaveBeenCalledWith(mockCollections);
		});

		it('should handle fetch errors', async () => {
			(fetch as jest.Mock).mockResolvedValueOnce({
				ok: false,
				json: async () => ({ error: 'Failed to fetch collections' }),
			});

			const mockFetchCollections = jest.fn().mockImplementation(async () => {
				const response = await fetch('/api/collections');
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error);
				}
			});

			mockContextValue.fetchCollections = mockFetchCollections;

			const { result } = renderHook(() => useCollections(), { wrapper });

			await expect(result.current.fetchCollections()).rejects.toThrow('Failed to fetch collections');
		});
	});

	describe('createCollection', () => {
		it('should create collection successfully', async () => {
			const newCollection = mockCollection();
			(fetch as jest.Mock).mockResolvedValueOnce({
				ok: true,
				json: async () => newCollection,
			});

			const mockCreateCollection = jest.fn().mockImplementation(async (name, targetLanguage, sourceLanguage, wordIds) => {
				const response = await fetch('/api/collections', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						name,
						target_language: targetLanguage,
						source_language: sourceLanguage,
						wordIds,
					}),
				});
				const data = await response.json();
				return data;
			});

			mockContextValue.createCollection = mockCreateCollection;

			const { result } = renderHook(() => useCollections(), { wrapper });

			let createdCollection;
			await act(async () => {
				createdCollection = await result.current.createCollection(
					'Test Collection',
					Language.EN,
					Language.VI,
					['word1', 'word2']
				);
			});

			expect(fetch).toHaveBeenCalledWith('/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					name: 'Test Collection',
					target_language: Language.EN,
					source_language: Language.VI,
					wordIds: ['word1', 'word2'],
				}),
			});
			expect(createdCollection).toEqual(newCollection);
		});

		it('should handle creation errors', async () => {
			(fetch as jest.Mock).mockResolvedValueOnce({
				ok: false,
				json: async () => ({ error: 'Collection name already exists' }),
			});

			const mockCreateCollection = jest.fn().mockImplementation(async (name, targetLanguage, sourceLanguage) => {
				const response = await fetch('/api/collections', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						name,
						target_language: targetLanguage,
						source_language: sourceLanguage,
					}),
				});
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error);
				}
			});

			mockContextValue.createCollection = mockCreateCollection;

			const { result } = renderHook(() => useCollections(), { wrapper });

			await expect(
				result.current.createCollection('Duplicate Collection', Language.EN, Language.VI)
			).rejects.toThrow('Collection name already exists');
		});
	});

	describe('addTermToCollection', () => {
		it('should add term to collection successfully', async () => {
			const collectionId = 'test-collection-id';
			const term = 'test';
			const language = Language.EN;
			const updatedCollection = mockCollection({ id: collectionId });

			(fetch as jest.Mock).mockResolvedValueOnce({
				ok: true,
				json: async () => updatedCollection,
			});

			const mockAddTermToCollection = jest.fn().mockImplementation(async (collectionId, term, language) => {
				const response = await fetch(`/api/collections/${collectionId}/add-term`, {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({ term, language }),
				});
				const data = await response.json();
				return data;
			});

			mockContextValue.addTermToCollection = mockAddTermToCollection;

			const { result } = renderHook(() => useCollections(), { wrapper });

			let updatedCollectionResult;
			await act(async () => {
				updatedCollectionResult = await result.current.addTermToCollection(collectionId, term, language);
			});

			expect(fetch).toHaveBeenCalledWith(`/api/collections/${collectionId}/add-term`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ term, language }),
			});
			expect(updatedCollectionResult).toEqual(updatedCollection);
		});
	});

	describe('fetchWordsByCollection', () => {
		it('should fetch words for collection successfully', async () => {
			const collectionId = 'test-collection-id';
			const mockWords = [mockWordDetail(), mockWordDetail()];

			(fetch as jest.Mock).mockResolvedValueOnce({
				ok: true,
				json: async () => mockWords,
			});

			const mockFetchWordsByCollection = jest.fn().mockImplementation(async (collectionId) => {
				const response = await fetch(`/api/collections/${collectionId}/vocabulary`);
				const data = await response.json();
				mockContextValue.setCurrentCollectionWords(data);
			});

			mockContextValue.fetchWordsByCollection = mockFetchWordsByCollection;
			mockContextValue.currentCollection = mockCollection({ id: collectionId });

			const { result } = renderHook(() => useCollections(), { wrapper });

			await act(async () => {
				await result.current.fetchWordsByCollection(collectionId);
			});

			expect(fetch).toHaveBeenCalledWith(`/api/collections/${collectionId}/vocabulary`);
			expect(mockContextValue.setCurrentCollectionWords).toHaveBeenCalledWith(mockWords);
		});

		it('should handle fetch words errors', async () => {
			const collectionId = 'test-collection-id';

			(fetch as jest.Mock).mockResolvedValueOnce({
				ok: false,
				json: async () => ({ error: 'Collection not found' }),
			});

			const mockFetchWordsByCollection = jest.fn().mockImplementation(async (collectionId) => {
				const response = await fetch(`/api/collections/${collectionId}/vocabulary`);
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error);
				}
			});

			mockContextValue.fetchWordsByCollection = mockFetchWordsByCollection;

			const { result } = renderHook(() => useCollections(), { wrapper });

			await expect(result.current.fetchWordsByCollection(collectionId)).rejects.toThrow('Collection not found');
		});
	});

	describe('deleteCollection', () => {
		it('should delete collection successfully', async () => {
			const collectionId = 'test-collection-id';

			(fetch as jest.Mock).mockResolvedValueOnce({
				ok: true,
				json: async () => ({ success: true }),
			});

			const mockDeleteCollection = jest.fn().mockImplementation(async (collectionId) => {
				const response = await fetch(`/api/collections/${collectionId}`, {
					method: 'DELETE',
				});
				return response.ok;
			});

			mockContextValue.deleteCollection = mockDeleteCollection;

			const { result } = renderHook(() => useCollections(), { wrapper });

			let deleteResult;
			await act(async () => {
				deleteResult = await result.current.deleteCollection(collectionId);
			});

			expect(fetch).toHaveBeenCalledWith(`/api/collections/${collectionId}`, {
				method: 'DELETE',
			});
			expect(deleteResult).toBe(true);
		});
	});
});
