'use client';

import {
	<PERSON><PERSON>,
	<PERSON>,
	Card<PERSON>ontent,
	Card<PERSON>eader,
	Card<PERSON>itle,
	LoadingSpinner,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Translate,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { useTranslation } from '@/contexts';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { RefreshCw, Filter } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { WordCard } from './word-card';

interface AllWordsResponse {
	words: WordDetail[];
	pagination: {
		page: number;
		limit: number;
		totalCount: number;
		totalPages: number;
		hasNextPage: boolean;
		hasPreviousPage: boolean;
	};
}

export function AllWordsSection() {
	const { t } = useTranslation();
	const { showError } = useToast();
	
	const [words, setWords] = useState<WordDetail[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalCount, setTotalCount] = useState(0);
	const [hasNextPage, setHasNextPage] = useState(false);
	const [selectedLanguage, setSelectedLanguage] = useState<Language | 'ALL'>('ALL');
	const [hasInitialLoad, setHasInitialLoad] = useState(false);

	const loadWords = useCallback(async (page: number = 1, language: Language | 'ALL' = 'ALL', append: boolean = false) => {
		if (append) {
			setIsLoadingMore(true);
		} else {
			setIsLoading(true);
		}

		try {
			const params = new URLSearchParams({
				page: page.toString(),
				limit: '20',
			});

			if (language !== 'ALL') {
				params.append('language', language);
			}

			const response = await fetch(`/api/words?${params}`);
			
			if (!response.ok) {
				throw new Error('Failed to load words');
			}

			const data: AllWordsResponse = await response.json();
			
			if (append) {
				setWords(prev => [...prev, ...data.words]);
			} else {
				setWords(data.words);
			}
			
			setCurrentPage(data.pagination.page);
			setTotalCount(data.pagination.totalCount);
			setHasNextPage(data.pagination.hasNextPage);
			setHasInitialLoad(true);
		} catch (error) {
			console.error('Failed to load words:', error);
			showError(t('vocabulary_lookup.search_error'));
		} finally {
			setIsLoading(false);
			setIsLoadingMore(false);
		}
	}, [t, showError]);

	// Initial load
	useEffect(() => {
		loadWords(1, selectedLanguage, false);
	}, [selectedLanguage, loadWords]);

	const handleLoadMore = useCallback(() => {
		if (hasNextPage && !isLoadingMore) {
			loadWords(currentPage + 1, selectedLanguage, true);
		}
	}, [hasNextPage, isLoadingMore, currentPage, selectedLanguage, loadWords]);

	const handleLanguageChange = useCallback((language: Language | 'ALL') => {
		setSelectedLanguage(language);
		setCurrentPage(1);
		setWords([]);
		setHasInitialLoad(false);
	}, []);

	const handleRefresh = useCallback(() => {
		setCurrentPage(1);
		setWords([]);
		setHasInitialLoad(false);
		loadWords(1, selectedLanguage, false);
	}, [selectedLanguage, loadWords]);

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<div>
						<CardTitle className="flex items-center gap-2">
							<Translate text="vocabulary_lookup.all_words_title" />
						</CardTitle>
						<p className="text-sm text-muted-foreground mt-1">
							<Translate text="vocabulary_lookup.all_words_subtitle" />
						</p>
					</div>
					<div className="flex items-center gap-2">
						<Filter className="h-4 w-4 text-muted-foreground" />
						<Select
							value={selectedLanguage}
							onValueChange={handleLanguageChange}
						>
							<SelectTrigger className="w-40">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="ALL">
									<Translate text="vocabulary_lookup.all_languages" />
								</SelectItem>
								<SelectItem value="EN">English</SelectItem>
								<SelectItem value="VI">Tiếng Việt</SelectItem>
							</SelectContent>
						</Select>
						<Button
							variant="outline"
							size="sm"
							onClick={handleRefresh}
							disabled={isLoading}
						>
							<RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
						</Button>
					</div>
				</div>
				
				{/* Word Count */}
				{hasInitialLoad && (
					<div className="text-sm text-muted-foreground">
						<Translate 
							text="vocabulary_lookup.word_count" 
							values={{ count: words.length.toString(), total: totalCount.toString() }}
						/>
					</div>
				)}
			</CardHeader>

			<CardContent>
				{/* Loading State */}
				{isLoading && !hasInitialLoad ? (
					<div className="flex items-center justify-center py-12">
						<LoadingSpinner className="h-8 w-8" />
						<span className="ml-2">
							<Translate text="vocabulary_lookup.loading" />
						</span>
					</div>
				) : words.length > 0 ? (
					<div className="space-y-4">
						{/* Words Grid */}
						<div className="grid gap-4">
							{words.map((word) => (
								<WordCard key={word.id} word={word} />
							))}
						</div>

						{/* Load More Button */}
						{hasNextPage && (
							<div className="flex justify-center pt-4">
								<Button
									onClick={handleLoadMore}
									disabled={isLoadingMore}
									variant="outline"
									className="px-8"
								>
									{isLoadingMore ? (
										<>
											<RefreshCw className="h-4 w-4 mr-2 animate-spin" />
											<Translate text="vocabulary_lookup.loading_more" />
										</>
									) : (
										<Translate text="vocabulary_lookup.load_more" />
									)}
								</Button>
							</div>
						)}

						{/* No More Words Message */}
						{!hasNextPage && words.length > 0 && (
							<div className="text-center py-4 text-sm text-muted-foreground">
								<Translate text="vocabulary_lookup.no_more_words" />
							</div>
						)}
					</div>
				) : hasInitialLoad ? (
					<div className="text-center py-12 text-muted-foreground">
						<Translate text="vocabulary_lookup.no_results" />
					</div>
				) : null}
			</CardContent>
		</Card>
	);
}
