import { jest, describe, beforeEach, it, expect } from '@jest/globals';
import { POST } from '../route';
import { NextRequest } from 'next/server';
import { Language, Difficulty } from '@prisma/client';
import { auth } from '@/lib/auth';
import { getLLMService } from '@/backend/wire';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/backend/wire');
jest.mock('node:fs/promises');

const mockAuth = auth as jest.MockedFunction<typeof auth>;
const mockGetLLMService = getLLMService as jest.MockedFunction<typeof getLLMService>;

const mockLLMService = {
	generateParagraph: jest.fn(),
};

describe('/api/llm/generate-paragraph', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		mockGetLLMService.mockResolvedValue(mockLLMService as any);
	});

	describe('POST /api/llm/generate-paragraph', () => {
		it('should generate paragraphs successfully', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const requestBody = {
				keywords: ['technology', 'innovation'],
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				count: 2,
				sentenceCount: 5,
			};
			const mockParagraphs = [
				'Technology drives innovation in modern society.',
				'Innovation creates new opportunities for technological advancement.',
			];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockLLMService.generateParagraph.mockResolvedValue(mockParagraphs);

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(requestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockLLMService.generateParagraph).toHaveBeenCalledWith({
				keywords: requestBody.keywords,
				language: requestBody.language,
				difficulty: requestBody.difficulty,
				count: requestBody.count,
				sentenceCount: requestBody.sentenceCount,
			});
			expect(data).toEqual(mockParagraphs);
		});

		it('should validate required fields', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const invalidRequestBody = {
				keywords: [], // Empty keywords array
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				count: 2,
			};

			mockAuth.mockResolvedValue({ user: mockUser } as any);

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(invalidRequestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('At least one keyword is required');
		});

		it('should validate keywords array length', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const invalidRequestBody = {
				keywords: [], // Empty array
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				count: 2,
			};

			mockAuth.mockResolvedValue({ user: mockUser } as any);

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(invalidRequestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('At least one keyword is required');
		});

		it('should validate maximum keywords', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const invalidRequestBody = {
				keywords: Array(11).fill('keyword'), // Too many keywords
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				count: 2,
			};

			mockAuth.mockResolvedValue({ user: mockUser } as any);

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(invalidRequestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Cannot use more than 10 keywords');
		});

		it('should validate count range', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const invalidRequestBody = {
				keywords: ['technology'],
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				count: 6, // Too many paragraphs
			};

			mockAuth.mockResolvedValue({ user: mockUser } as any);

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(invalidRequestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Cannot generate more than 5 paragraphs at once');
		});

		it('should validate language enum', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const invalidRequestBody = {
				keywords: ['technology'],
				language: 'INVALID_LANGUAGE',
				difficulty: Difficulty.INTERMEDIATE,
				count: 2,
			};

			mockAuth.mockResolvedValue({ user: mockUser } as any);

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(invalidRequestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toBeDefined();
		});

		it('should validate difficulty enum', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const invalidRequestBody = {
				keywords: ['technology'],
				language: Language.EN,
				difficulty: 'INVALID_DIFFICULTY',
				count: 2,
			};

			mockAuth.mockResolvedValue({ user: mockUser } as any);

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(invalidRequestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toBeDefined();
		});

		it('should return 401 when not authenticated', async () => {
			mockAuth.mockResolvedValue(null);

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					keywords: ['technology'],
					language: Language.EN,
					difficulty: Difficulty.INTERMEDIATE,
					count: 2,
				}),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toBe('User not authenticated for generating paragraphs.');
		});

		it('should handle LLM service errors', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const requestBody = {
				keywords: ['technology'],
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				count: 2,
			};

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockLLMService.generateParagraph.mockRejectedValue(new Error('LLM service error'));

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(requestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data.error).toBe('Failed to generate paragraph. Please try again.');
		});

		it('should handle optional sentenceCount parameter', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const requestBody = {
				keywords: ['technology'],
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				count: 1,
				sentenceCount: 10,
			};
			const mockParagraphs = ['Technology is transforming our world in unprecedented ways.'];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockLLMService.generateParagraph.mockResolvedValue(mockParagraphs);

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(requestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockLLMService.generateParagraph).toHaveBeenCalledWith({
				keywords: requestBody.keywords,
				language: requestBody.language,
				difficulty: requestBody.difficulty,
				count: requestBody.count,
				sentenceCount: requestBody.sentenceCount,
			});
			expect(data).toEqual(mockParagraphs);
		});

		it('should work without optional sentenceCount parameter', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const requestBody = {
				keywords: ['technology'],
				language: Language.EN,
				difficulty: Difficulty.INTERMEDIATE,
				count: 1,
			};
			const mockParagraphs = ['Technology is transforming our world.'];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockLLMService.generateParagraph.mockResolvedValue(mockParagraphs);

			const request = new NextRequest('http://localhost:3000/api/llm/generate-paragraph', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(requestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockLLMService.generateParagraph).toHaveBeenCalledWith({
				keywords: requestBody.keywords,
				language: requestBody.language,
				difficulty: requestBody.difficulty,
				count: requestBody.count,
				sentenceCount: undefined,
			});
			expect(data).toEqual(mockParagraphs);
		});
	});
});
