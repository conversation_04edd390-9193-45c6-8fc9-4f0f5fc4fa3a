import { jest, describe, beforeEach, it, expect } from '@jest/globals';
import { GET, POST } from '../route';
import { NextRequest } from 'next/server';
import { Language } from '@prisma/client';
import { auth } from '@/lib/auth';
import { getCollectionService } from '@/backend/wire';
import { mockCollection, mockUser } from '@/test/fixtures';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/backend/wire');

const mockAuth = auth as jest.MockedFunction<typeof auth>;
const mockGetCollectionService = getCollectionService as jest.MockedFunction<
	typeof getCollectionService
>;

const mockCollectionService = {
	getUserCollections: jest.fn(),
	createCollection: jest.fn(),
	getAllCollections: jest.fn(),
};

// Helper function to serialize dates like JSON.stringify does
function serializeDates(obj: any): any {
	return JSON.parse(JSON.stringify(obj));
}

describe('/api/collections', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		mockGetCollectionService.mockReturnValue(mockCollectionService as any);
	});

	describe('GET /api/collections', () => {
		it('should return user collections when authenticated', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const mockCollections = [
				mockCollection({ user_id: mockUser.id }),
				mockCollection({ user_id: mockUser.id }),
			];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionService.getUserCollections.mockResolvedValue(mockCollections);

			const request = new NextRequest('http://localhost:3000/api/collections');
			const response = await GET(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockCollectionService.getUserCollections).toHaveBeenCalledWith(mockUser.id);
			expect(data).toEqual(serializeDates(mockCollections));
		});

		it('should return 401 when not authenticated', async () => {
			mockAuth.mockResolvedValue(null);

			const request = new NextRequest('http://localhost:3000/api/collections');
			const response = await GET(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toBe('User not authenticated');
		});

		it('should handle service errors', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionService.getUserCollections.mockRejectedValue(new Error('Service error'));

			const request = new NextRequest('http://localhost:3000/api/collections');
			const response = await GET(request);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data.error).toBe('Failed to fetch collections. Please try again.');
		});
	});

	describe('POST /api/collections', () => {
		it('should create collection successfully', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const requestBody = {
				name: 'Test Collection',
				target_language: Language.EN,
				source_language: Language.VI,
				wordIds: ['word1', 'word2'],
			};
			const mockCreatedCollection = mockCollection({
				...requestBody,
				user_id: mockUser.id,
			});

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionService.createCollection.mockResolvedValue(mockCreatedCollection);

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'X-Forwarded-For': '127.0.0.1',
					'User-Agent': 'test-agent',
				},
				body: JSON.stringify(requestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockCollectionService.createCollection).toHaveBeenCalledWith(
				mockUser.id,
				requestBody.name,
				requestBody.target_language,
				requestBody.source_language,
				requestBody.wordIds,
				'127.0.0.1',
				'test-agent'
			);
			expect(data).toEqual(serializeDates(mockCreatedCollection));
		});

		it('should validate required fields', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const invalidRequestBody = {
				// Missing name
				target_language: Language.EN,
				source_language: Language.VI,
			};

			mockAuth.mockResolvedValue({ user: mockUser } as any);

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(invalidRequestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('name');
		});

		it('should validate language enums', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const invalidRequestBody = {
				name: 'Test Collection',
				target_language: 'INVALID_LANGUAGE',
				source_language: Language.VI,
			};

			mockAuth.mockResolvedValue({ user: mockUser } as any);

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(invalidRequestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Invalid target language');
		});

		it('should return 401 when not authenticated', async () => {
			mockAuth.mockResolvedValue(null);

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					name: 'Test Collection',
					target_language: Language.EN,
					source_language: Language.VI,
				}),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toBe('Unauthorized');
		});

		it('should handle service errors', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const requestBody = {
				name: 'Test Collection',
				target_language: Language.EN,
				source_language: Language.VI,
			};

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionService.createCollection.mockRejectedValue(new Error('Service error'));

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(requestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data.error).toBe('Failed to create collection. Please try again.');
		});

		it('should handle JSON parsing errors', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser } as any);

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: 'invalid json',
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data.error).toBe('Failed to create collection. Please try again.');
		});

		it('should create collection without wordIds', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const requestBody = {
				name: 'Test Collection',
				target_language: Language.EN,
				source_language: Language.VI,
			};
			const mockCreatedCollection = mockCollection({
				...requestBody,
				user_id: mockUser.id,
				word_ids: [],
			});

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionService.createCollection.mockResolvedValue(mockCreatedCollection);

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(requestBody),
			});

			const response = await POST(request);
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockCollectionService.createCollection).toHaveBeenCalledWith(
				mockUser.id,
				requestBody.name,
				requestBody.target_language,
				requestBody.source_language,
				undefined, // wordIds should be undefined
				expect.any(String), // IP address
				expect.any(String) // User agent
			);
			expect(data).toEqual(serializeDates(mockCreatedCollection));
		});
	});
});
