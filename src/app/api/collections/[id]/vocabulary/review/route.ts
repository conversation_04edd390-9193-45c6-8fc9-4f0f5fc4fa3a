import { NotFoundError, UnauthorizedError, ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { auth } from '@/lib';
import { WordDetail } from '@/models';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	const resolvedParams = await params;
	const collectionId = resolvedParams.id;

	try {
		const session = await auth();
		const userId = session?.user?.id;

		if (!userId) {
			throw new UnauthorizedError(
				'Unauthorized: User must be authenticated to fetch words for review.'
			);
		}

		if (!collectionId) {
			throw new ValidationError('Collection ID is required to fetch words for review.');
		}

		const { searchParams } = new URL(request.url);
		const limitParam = searchParams.get('limit');
		const limit = limitParam ? parseInt(limitParam, 10) : undefined;

		if (limit !== undefined && (isNaN(limit) || limit < 1 || limit > 100)) {
			throw new ValidationError('Limit must be a number between 1 and 100.');
		}

		const wordService = getWordService();
		const words = await wordService.getWordsToReview(userId, collectionId, limit);

		return NextResponse.json(words as WordDetail[]);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		if (error instanceof NotFoundError) {
			return NextResponse.json({ error: error.message }, { status: 404 });
		}

		console.error(`Failed to fetch words for review in collection ${collectionId}:`, error);
		return NextResponse.json(
			{ error: 'Failed to fetch words for review. Please try again.' },
			{ status: 500 }
		);
	}
}
