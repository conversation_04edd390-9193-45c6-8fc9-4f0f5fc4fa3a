import { GET } from '../route';
import { NextRequest } from 'next/server';
import { auth } from '@/lib/auth';
import { getCollectionStatsService } from '@/backend/wire';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/backend/wire');

const mockAuth = auth as jest.MockedFunction<typeof auth>;
const mockGetCollectionStatsService = getCollectionStatsService as jest.MockedFunction<typeof getCollectionStatsService>;

const mockCollectionStatsService = {
	getStatsForCollection: jest.fn(),
};

describe('/api/collections/[id]/stats', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		mockGetCollectionStatsService.mockReturnValue(mockCollectionStatsService as any);
	});

	describe('GET /api/collections/[id]/stats', () => {
		it('should return collection stats successfully', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const collectionId = 'test-collection-id';
			const mockStats = [
				{
					date: '2024-01-01',
					wordsReviewed: 5,
					qaPracticeSubmissions: 2,
					paragraphPracticeSubmissions: 1,
				},
				{
					date: '2024-01-02',
					wordsReviewed: 3,
					qaPracticeSubmissions: 1,
					paragraphPracticeSubmissions: 2,
				},
			];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionStatsService.getStatsForCollection.mockResolvedValue(mockStats);

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockCollectionStatsService.getStatsForCollection).toHaveBeenCalledWith(
				collectionId,
				mockUser.id,
				undefined // default days
			);
			expect(data).toEqual(mockStats);
		});

		it('should return collection stats with custom days parameter', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const collectionId = 'test-collection-id';
			const days = 14;
			const mockStats = [
				{
					date: '2024-01-01',
					wordsReviewed: 10,
					qaPracticeSubmissions: 5,
					paragraphPracticeSubmissions: 3,
				},
			];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionStatsService.getStatsForCollection.mockResolvedValue(mockStats);

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats?days=${days}`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockCollectionStatsService.getStatsForCollection).toHaveBeenCalledWith(
				collectionId,
				mockUser.id,
				days
			);
			expect(data).toEqual(mockStats);
		});

		it('should handle invalid days parameter', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const collectionId = 'test-collection-id';
			const mockStats = [];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionStatsService.getStatsForCollection.mockResolvedValue(mockStats);

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats?days=invalid`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockCollectionStatsService.getStatsForCollection).toHaveBeenCalledWith(
				collectionId,
				mockUser.id,
				undefined // invalid days should be treated as undefined
			);
			expect(data).toEqual(mockStats);
		});

		it('should return 401 when not authenticated', async () => {
			const collectionId = 'test-collection-id';
			mockAuth.mockResolvedValue(null);

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toBe('Unauthorized');
		});

		it('should return 401 when user id is missing', async () => {
			const collectionId = 'test-collection-id';
			mockAuth.mockResolvedValue({ user: {} } as any); // user without id

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toBe('Unauthorized');
		});

		it('should handle service errors', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const collectionId = 'test-collection-id';

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionStatsService.getStatsForCollection.mockRejectedValue(new Error('Service error'));

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data.error).toBe('Internal server error');
		});

		it('should return empty stats array when no data available', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const collectionId = 'test-collection-id';
			const mockStats: any[] = [];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionStatsService.getStatsForCollection.mockResolvedValue(mockStats);

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toEqual([]);
		});

		it('should handle zero days parameter', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const collectionId = 'test-collection-id';
			const days = 0;
			const mockStats: any[] = [];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionStatsService.getStatsForCollection.mockResolvedValue(mockStats);

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats?days=${days}`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockCollectionStatsService.getStatsForCollection).toHaveBeenCalledWith(
				collectionId,
				mockUser.id,
				days
			);
			expect(data).toEqual(mockStats);
		});

		it('should handle negative days parameter', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const collectionId = 'test-collection-id';
			const days = -5;
			const mockStats: any[] = [];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionStatsService.getStatsForCollection.mockResolvedValue(mockStats);

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats?days=${days}`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockCollectionStatsService.getStatsForCollection).toHaveBeenCalledWith(
				collectionId,
				mockUser.id,
				days
			);
			expect(data).toEqual(mockStats);
		});

		it('should handle large days parameter', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const collectionId = 'test-collection-id';
			const days = 365;
			const mockStats = [
				{
					date: '2023-01-01',
					wordsReviewed: 100,
					qaPracticeSubmissions: 50,
					paragraphPracticeSubmissions: 25,
				},
			];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionStatsService.getStatsForCollection.mockResolvedValue(mockStats);

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats?days=${days}`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockCollectionStatsService.getStatsForCollection).toHaveBeenCalledWith(
				collectionId,
				mockUser.id,
				days
			);
			expect(data).toEqual(mockStats);
		});

		it('should handle multiple query parameters', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			const collectionId = 'test-collection-id';
			const days = 7;
			const mockStats: any[] = [];

			mockAuth.mockResolvedValue({ user: mockUser } as any);
			mockCollectionStatsService.getStatsForCollection.mockResolvedValue(mockStats);

			const request = new NextRequest(`http://localhost:3000/api/collections/${collectionId}/stats?days=${days}&other=param`);
			const response = await GET(request, { params: Promise.resolve({ id: collectionId }) });
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(mockCollectionStatsService.getStatsForCollection).toHaveBeenCalledWith(
				collectionId,
				mockUser.id,
				days
			);
			expect(data).toEqual(mockStats);
		});
	});
});
