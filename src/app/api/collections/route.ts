import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getCollectionService } from '@/backend/wire';
import { auth } from '@/lib';
import { createErrorContext, errorLogger } from '@/lib/error-handling';
import { CollectionWithDetail } from '@/models';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { extractIpAddress, extractUserAgent } from '@/backend/utils/audit.helper';

export async function GET() {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) {
			throw new UnauthorizedError('User not authenticated');
		}

		const collectionService = getCollectionService();
		const collections = await collectionService.getUserCollections(userId);

		return NextResponse.json(collections as CollectionWithDetail[]);
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		errorLogger.error(
			'Failed to get collections',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('CollectionAPI', 'get_collections'),
			'CollectionAPI'
		);

		return NextResponse.json(
			{ error: 'Failed to fetch collections. Please try again.' },
			{ status: 500 }
		);
	}
}

export async function POST(request: NextRequest) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('Unauthorized');

		const body = await request.json();
		const { name, target_language, source_language, wordIds } = body;

		if (!name) {
			throw new ValidationError('Collection name is required');
		}

		if (!target_language) {
			throw new ValidationError('Target language is required');
		}

		if (!source_language) {
			throw new ValidationError('Source language is required');
		}

		if (!Object.values(Language).includes(target_language)) {
			throw new ValidationError(`Invalid target language: ${target_language}`);
		}

		if (!Object.values(Language).includes(source_language)) {
			throw new ValidationError(`Invalid source language: ${source_language}`);
		}

		const collectionService = getCollectionService();
		const ipAddress = extractIpAddress(request);
		const userAgent = extractUserAgent(request);

		const collection = await collectionService.createCollection(
			userId,
			name,
			target_language,
			source_language,
			wordIds,
			ipAddress,
			userAgent
		);

		return NextResponse.json(collection as CollectionWithDetail);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		errorLogger.error(
			'Failed to create collection',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('CollectionAPI', 'create_collection'),
			'CollectionAPI'
		);

		return NextResponse.json(
			{ error: 'Failed to create collection. Please try again.' },
			{ status: 500 }
		);
	}
}
