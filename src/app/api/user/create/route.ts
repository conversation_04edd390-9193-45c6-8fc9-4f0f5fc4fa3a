import { ValidationError } from '@/backend/errors';
import { getUserService } from '@/backend/wire';
import { Provider } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { extractIpAddress, extractUserAgent } from '@/backend/utils/audit.helper';

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { provider, providerId, email, name } = body;

		if (!body || typeof body !== 'object') {
			throw new ValidationError('Data object is required to create a user.');
		}

		if (!provider) {
			throw new ValidationError('Provider is required to create a user.');
		}

		if (!providerId) {
			throw new ValidationError('Provider ID is required to create a user.');
		}

		if (!Object.values(Provider).includes(provider)) {
			throw new ValidationError(`Invalid provider: ${provider}`);
		}

		const userService = getUserService();
		const ipAddress = extractIpAddress(request);
		const userAgent = extractUserAgent(request);

		const userObject = await userService.createUser(
			{
				provider: provider,
				provider_id: providerId,
				email: email,
				name: name,
			},
			ipAddress,
			userAgent
		);

		return NextResponse.json({ id: userObject.id });
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error('Failed to create user:', error);
		return NextResponse.json(
			{ error: 'Failed to create user. Please try again.' },
			{ status: 500 }
		);
	}
}
