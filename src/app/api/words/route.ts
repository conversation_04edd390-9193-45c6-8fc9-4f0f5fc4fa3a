import { ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { WordDetail } from '@/models';
import { Language, Prisma } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const pageParam = searchParams.get('page');
		const limitParam = searchParams.get('limit');
		const language = searchParams.get('language') as Language | null;

		// Validate and set pagination parameters
		const page = pageParam ? parseInt(pageParam, 10) : 1;
		const limit = limitParam ? parseInt(limitParam, 10) : 50;

		if (isNaN(page) || page < 1) {
			throw new ValidationError('Page must be a positive number.');
		}

		if (isNaN(limit) || limit < 1 || limit > 100) {
			throw new ValidationError('Limit must be a number between 1 and 100.');
		}

		// Validate language parameter
		if (language && !Object.values(Language).includes(language)) {
			throw new ValidationError(`Invalid language: ${language}`);
		}

		const wordService = getWordService();

		// Build query for filtering
		const query: Prisma.WordWhereInput = {};
		if (language) {
			query.language = language;
		}

		// Calculate total limit including pagination
		const totalLimit = page * limit;

		// Prioritize words with WordNet data
		query.wordnet_data = {
			isNot: null,
		};

		// Get words with limit (we'll slice for pagination)
		const allWords = await wordService.getWordRepository().find(query, totalLimit);

		// Apply pagination by slicing
		const startIndex = (page - 1) * limit;
		const endIndex = startIndex + limit;
		const paginatedWords = allWords.slice(startIndex, endIndex);

		// Words already have WordNet data from database, convert to WordDetail
		const enrichedWords = paginatedWords.map((word) => ({
			...word,
			wordnet_data: word.wordnet_data
				? {
						synsets: word.wordnet_data.synsets || [],
						lemma: word.wordnet_data.lemma,
						hypernyms: word.wordnet_data.hypernyms || [],
						hyponyms: word.wordnet_data.hyponyms || [],
						holonyms: word.wordnet_data.holonyms || [],
						meronyms: word.wordnet_data.meronyms || [],
				  }
				: null,
		}));

		// For total count, we need to get all words and count them
		// This is not optimal but works with current repository structure
		const allWordsForCount = await wordService.getWordRepository().find(query);
		const totalCount = allWordsForCount.length;
		const totalPages = Math.ceil(totalCount / limit);

		const response = {
			words: enrichedWords as WordDetail[],
			pagination: {
				page,
				limit,
				totalCount,
				totalPages,
				hasNextPage: page < totalPages,
				hasPreviousPage: page > 1,
			},
		};

		return NextResponse.json(response);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error('Failed to fetch words:', error);
		return NextResponse.json(
			{ error: 'Failed to fetch words. Please try again.' },
			{ status: 500 }
		);
	}
}
