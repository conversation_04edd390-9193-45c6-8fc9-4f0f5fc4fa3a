import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAdminService } from '@/backend/wire';
import { z } from 'zod';
import { extractIpAddress, extractUserAgent } from '@/backend/utils/audit.helper';

// Validation schema
const bulkDeleteFeedbackSchema = z.object({
	feedbackIds: z.array(z.string().uuid()).min(1).max(100), // Limit to 100 feedback items at once
});

/**
 * POST /api/admin/feedback/bulk-delete
 * Bulk delete multiple feedback items
 */
async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { feedbackIds } = bulkDeleteFeedbackSchema.parse(body);

		const adminService = getAdminService();
		const ipAddress = extractIpAddress(request);
		const userAgent = extractUserAgent(request);
		const adminId = request.headers.get('x-user-id') || undefined;

		// Perform bulk delete
		const results = await adminService.bulkDeleteFeedback(
			feedbackIds,
			adminId,
			ipAddress,
			userAgent
		);

		return NextResponse.json({
			success: true,
			data: results,
			message: `Successfully deleted ${results.success} feedback items. ${results.failed} failed.`,
		});
	} catch (error) {
		console.error('Error in bulk delete feedback:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to bulk delete feedback',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
export const wrappedPOST = withAdminAuth(withErrorHandling(POST));
export { wrappedPOST as POST };
