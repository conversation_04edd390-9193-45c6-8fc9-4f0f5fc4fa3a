import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAdminService } from '@/backend/wire';
import { z } from 'zod';
import { extractIpAddress, extractUserAgent } from '@/backend/utils/audit.helper';

// Validation schemas
const updateFeedbackSchema = z.object({
	status: z.enum(['pending', 'reviewed', 'resolved', 'dismissed']),
});

/**
 * GET /api/admin/feedback/[id]
 * Get specific feedback details by ID
 */
async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	try {
		const { id } = await params;

		if (!id) {
			return NextResponse.json(
				{ success: false, error: 'Feedback ID is required' },
				{ status: 400 }
			);
		}

		const adminService = getAdminService();
		const feedbacks = await adminService.getAllFeedback(1, 1000); // Get all to find specific one
		const feedback = feedbacks.feedbacks.find((f) => f.id === id);

		if (!feedback) {
			return NextResponse.json(
				{ success: false, error: 'Feedback not found' },
				{ status: 404 }
			);
		}

		return NextResponse.json({
			success: true,
			data: feedback,
		});
	} catch (error) {
		console.error('Error fetching feedback:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch feedback',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

/**
 * PATCH /api/admin/feedback/[id]
 * Update feedback status
 */
async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	try {
		const { id: feedbackId } = await params;
		if (!feedbackId) {
			return NextResponse.json(
				{ success: false, error: 'Feedback ID is required' },
				{ status: 400 }
			);
		}

		const body = await request.json();
		const { status } = updateFeedbackSchema.parse(body);

		const adminService = getAdminService();
		const ipAddress = extractIpAddress(request);
		const userAgent = extractUserAgent(request);
		const adminId = request.headers.get('x-user-id') || undefined;

		// Update feedback status
		const updatedFeedback = await adminService.updateFeedbackStatus(
			feedbackId,
			status,
			adminId,
			ipAddress,
			userAgent
		);

		return NextResponse.json({
			success: true,
			data: updatedFeedback,
			message: 'Feedback status updated successfully',
		});
	} catch (error) {
		console.error('Error updating feedback:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to update feedback',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

/**
 * DELETE /api/admin/feedback/[id]
 * Delete feedback
 */
async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	try {
		const { id: feedbackId } = await params;
		if (!feedbackId) {
			return NextResponse.json(
				{ success: false, error: 'Feedback ID is required' },
				{ status: 400 }
			);
		}

		const adminService = getAdminService();
		const ipAddress = extractIpAddress(request);
		const userAgent = extractUserAgent(request);
		const adminId = request.headers.get('x-user-id') || undefined;

		// Delete feedback
		await adminService.deleteFeedback(feedbackId, adminId, ipAddress, userAgent);

		return NextResponse.json({
			success: true,
			message: 'Feedback deleted successfully',
		});
	} catch (error) {
		console.error('Error deleting feedback:', error);

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to delete feedback',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
const wrappedGET = withAdminAuth(withErrorHandling(GET));
const wrappedPATCH = withAdminAuth(withErrorHandling(PATCH));
const wrappedDELETE = withAdminAuth(withErrorHandling(DELETE));

export { wrappedGET as GET, wrappedPATCH as PATCH, wrappedDELETE as DELETE };
