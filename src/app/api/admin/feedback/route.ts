import { ServerError } from '@/backend/errors';
import { getAdminService } from '@/backend/wire';
import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { z } from 'zod';

// Validation schema for query parameters
const getFeedbackQuerySchema = z.object({
	page: z
		.string()
		.nullable()
		.optional()
		.transform((val) => (val ? parseInt(val, 10) : 1)),
	limit: z
		.string()
		.nullable()
		.optional()
		.transform((val) => (val ? parseInt(val, 10) : 50)),
	search: z
		.string()
		.nullable()
		.optional()
		.transform((val) => val || undefined),
	status: z
		.string()
		.nullable()
		.optional()
		.transform((val) => val || undefined),
});

async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const query = getFeedbackQuerySchema.parse({
			page: searchParams.get('page'),
			limit: searchParams.get('limit'),
			search: searchParams.get('search'),
			status: searchParams.get('status'),
		});

		const adminService = getAdminService();
		const result = await adminService.getAllFeedback(
			query.page,
			query.limit,
			query.search,
			query.status
		);

		return NextResponse.json({
			success: true,
			data: result,
		});
	} catch (error) {
		console.error('Error fetching feedback:', error);

		if (error instanceof ServerError) {
			return NextResponse.json({ error: error.message }, { status: error.statusCode });
		}

		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}

async function PATCH(request: NextRequest) {
	try {
		const { id, status } = await request.json();

		if (!id || !status) {
			return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
		}

		const adminService = getAdminService();
		const adminId = request.headers.get('x-user-id') || undefined;

		const updatedFeedback = await adminService.updateFeedbackStatus(id, status, adminId);

		return NextResponse.json(updatedFeedback);
	} catch (error) {
		console.error('Error updating feedback:', error);

		if (error instanceof ServerError) {
			return NextResponse.json({ error: error.message }, { status: error.statusCode });
		}

		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}

// Apply admin authentication middleware
const wrappedGET = withAdminAuth(withErrorHandling(GET));
const wrappedPATCH = withAdminAuth(withErrorHandling(PATCH));

export { wrappedGET as GET, wrappedPATCH as PATCH };
