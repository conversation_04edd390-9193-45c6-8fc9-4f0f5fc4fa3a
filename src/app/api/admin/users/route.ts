import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAdminService } from '@/backend/wire';
import { Role, Provider } from '@prisma/client';
import { z } from 'zod';
import { extractIpAddress, extractUserAgent } from '@/backend/utils/audit.helper';

// Validation schemas
const getUsersQuerySchema = z.object({
	page: z
		.string()
		.nullable()
		.optional()
		.transform((val) => (val ? parseInt(val, 10) : 1)),
	limit: z
		.string()
		.nullable()
		.optional()
		.transform((val) => (val ? parseInt(val, 10) : 50)),
	search: z.string().nullable().optional(),
});

const updateUserSchema = z.object({
	userId: z.string().uuid(),
	role: z.nativeEnum(Role).optional(),
	disabled: z.boolean().optional(),
});

const createAdminSchema = z.object({
	username: z.string().min(3).max(50),
	password: z.string().min(6),
});

const createUserSchema = z.object({
	username: z.string().min(3).max(50).optional(),
	password: z.string().min(6).optional(),
	email: z.string().email().optional(),
	name: z.string().min(1).max(100).optional(),
	provider: z.nativeEnum(Provider),
	role: z.nativeEnum(Role),
});

/**
 * GET /api/admin/users
 * Get paginated list of all users with filtering
 */
async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const query = getUsersQuerySchema.parse({
			page: searchParams.get('page'),
			limit: searchParams.get('limit'),
			search: searchParams.get('search'),
		});

		const adminService = getAdminService();
		const result = await adminService.getAllUsers(
			query.page,
			query.limit,
			query.search || undefined
		);

		return NextResponse.json({
			success: true,
			data: result,
		});
	} catch (error) {
		console.error('Error fetching users:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch users',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

/**
 * POST /api/admin/users
 * Create new admin user
 */
async function POST(request: NextRequest) {
	try {
		const body = await request.json();

		// Try to parse as new user schema first, fallback to admin schema for backward compatibility
		let userData;
		try {
			userData = createUserSchema.parse(body);
		} catch {
			// Fallback to admin schema for backward compatibility
			const { username, password } = createAdminSchema.parse(body);
			userData = {
				username,
				password,
				provider: Provider.USERNAME_PASSWORD,
				role: Role.ADMIN,
			};
		}

		const { username, password, email, name, provider, role } = userData;

		// Validate required fields based on provider
		if (provider === Provider.USERNAME_PASSWORD) {
			if (!username || !password) {
				return NextResponse.json(
					{
						success: false,
						error: 'Username and password are required for USERNAME_PASSWORD provider',
					},
					{ status: 400 }
				);
			}
		}

		const adminService = getAdminService();
		const ipAddress = extractIpAddress(request);
		const userAgent = extractUserAgent(request);

		let user;

		if (provider === Provider.USERNAME_PASSWORD) {
			// Hash password (password is guaranteed to be defined due to validation above)
			const bcrypt = await import('bcryptjs');
			const passwordHash = await bcrypt.hash(password!, 12);

			if (role === Role.ADMIN) {
				user = await adminService.createAdminUser(
					{
						username: username!,
						password_hash: passwordHash,
						provider,
						provider_id: username!,
					},
					undefined, // adminId will be extracted from middleware
					ipAddress,
					userAgent
				);
			} else {
				user = await adminService.createUserWithPassword(
					{
						username: username!,
						password_hash: passwordHash,
						provider,
						provider_id: username!,
						role,
					},
					undefined, // adminId will be extracted from middleware
					ipAddress,
					userAgent
				);
			}
		} else {
			// For other providers, create user without password
			user = await adminService.createUser(
				{
					provider,
					provider_id: email || name || 'unknown',
					email,
					name,
					role,
				},
				undefined, // adminId will be extracted from middleware
				ipAddress,
				userAgent
			);
		}

		return NextResponse.json(
			{
				success: true,
				data: {
					id: user.id,
					username: user.username,
					provider: user.provider,
					provider_id: user.provider_id,
					role: user.role,
					disabled: user.disabled,
					created_at: user.created_at,
				},
			},
			{ status: 201 }
		);
	} catch (error) {
		console.error('Error creating user:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		// Handle Prisma unique constraint errors
		if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
			return NextResponse.json(
				{ success: false, error: 'User with this username or email already exists' },
				{ status: 409 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to create user',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

/**
 * PATCH /api/admin/users
 * Update user role or status
 */
async function PATCH(request: NextRequest) {
	try {
		const body = await request.json();
		const { userId, role, disabled } = updateUserSchema.parse(body);

		const adminService = getAdminService();
		let updatedUser;

		if (role !== undefined) {
			updatedUser = await adminService.updateUserRole(userId, role);
		}

		if (disabled !== undefined) {
			if (disabled) {
				updatedUser = await adminService.disableUser(userId);
			} else {
				updatedUser = await adminService.enableUser(userId);
			}
		}

		return NextResponse.json({
			success: true,
			data: updatedUser,
		});
	} catch (error) {
		console.error('Error updating user:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to update user',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
const wrappedGET = withAdminAuth(withErrorHandling(GET));
const wrappedPOST = withAdminAuth(withErrorHandling(POST));
const wrappedPATCH = withAdminAuth(withErrorHandling(PATCH));

export { wrappedGET as GET, wrappedPOST as POST, wrappedPATCH as PATCH };
