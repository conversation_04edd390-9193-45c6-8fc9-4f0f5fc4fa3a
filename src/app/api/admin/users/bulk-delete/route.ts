import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAdminService } from '@/backend/wire';
import { z } from 'zod';
import { extractIpAddress, extractUserAgent } from '@/backend/utils/audit.helper';

// Validation schema
const bulkDeleteUsersSchema = z.object({
	userIds: z.array(z.string().uuid()).min(1).max(50), // Limit to 50 users at once
});

/**
 * POST /api/admin/users/bulk-delete
 * Bulk delete multiple users
 */
async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { userIds } = bulkDeleteUsersSchema.parse(body);

		const adminService = getAdminService();
		const ipAddress = extractIpAddress(request);
		const userAgent = extractUserAgent(request);
		const adminId = request.headers.get('x-user-id') || undefined;

		// Perform bulk delete
		const results = await adminService.bulkDeleteUsers(
			userIds,
			adminId,
			ipAddress,
			userAgent
		);

		return NextResponse.json({
			success: true,
			data: results,
			message: `Successfully deleted ${results.success} users. ${results.failed} failed.`,
		});
	} catch (error) {
		console.error('Error in bulk delete users:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to bulk delete users',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
export const wrappedPOST = withAdminAuth(withErrorHandling(POST));
export { wrappedPOST as POST };
