import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { UnauthorizedError } from '@/backend/errors';
import { verifyToken } from '@/backend/utils/token.util';
import { getCacheService } from '@/backend/wire';

/**
 * GET /api/token-monitor/cache-stats
 * Get cache performance statistics
 */
async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		// Get cache service and stats
		let cacheStats;
		try {
			const cacheService = await getCacheService();
			cacheStats = await cacheService.getStats();
		} catch (error) {
			console.warn('Failed to get cache stats, using defaults:', error);
			// Provide default stats if cache service is not available
			cacheStats = {
				hits: 0,
				misses: 0,
				keys: 0,
				hitRate: 0,
				memoryUsage: 0,
			};
		}

		// Transform to expected frontend format
		const response = {
			regular: {
				hits: cacheStats.hits || 0,
				misses: cacheStats.misses || 0,
				keys: cacheStats.keys || 0,
				hitRate: cacheStats.hitRate || 0,
				memoryUsage: cacheStats.memoryUsage || 0,
			},
			semantic: {
				enabled: false, // TODO: Implement semantic caching
				totalEntries: 0,
				totalKeywords: 0,
				expiredCount: 0,
				averageAccess: 0,
			},
			combined: {
				totalEntries: cacheStats.keys || 0,
				hitRate: cacheStats.hitRate || 0,
				memoryUsage: cacheStats.memoryUsage || 0,
				keywordIndex: 0,
				expiredEntries: 0,
			},
		};

		return NextResponse.json({
			success: true,
			data: response,
		});
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ success: false, error: error.message }, { status: 401 });
		}

		console.error('Error getting cache stats:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

const wrappedGET = withErrorHandling(GET);
export { wrappedGET as GET };
