import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { UnauthorizedError } from '@/backend/errors';
import { verifyToken } from '@/backend/utils/token.util';
import { getCacheService } from '@/backend/wire';

/**
 * POST /api/token-monitor/cleanup-cache
 * Clean up expired cache entries
 */
async function POST(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		// Get cache service and perform cleanup
		let cleanedEntries = 0;
		try {
			const cacheService = await getCacheService();

			// Get current stats before cleanup
			const statsBefore = await cacheService.getStats();

			// For now, we don't have a specific cleanup method,
			// so we'll just report the current state
			// TODO: Implement actual cache cleanup logic

			const statsAfter = await cacheService.getStats();
			cleanedEntries = Math.max(0, statsBefore.keys - statsAfter.keys);
		} catch (error) {
			console.warn('Failed to cleanup cache:', error);
		}

		return NextResponse.json({
			success: true,
			data: {
				cleanedEntries,
				message: `Cache cleanup completed. ${cleanedEntries} entries cleaned.`,
			},
		});
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ success: false, error: error.message }, { status: 401 });
		}

		console.error('Error cleaning up cache:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

const wrappedPOST = withErrorHandling(POST);
export { wrappedPOST as POST };
