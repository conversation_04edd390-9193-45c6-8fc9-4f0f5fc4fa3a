import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { UnauthorizedError } from '@/backend/errors';
import { verifyToken } from '@/backend/utils/token.util';

/**
 * GET /api/token-monitor/model-stats
 * Get model selection statistics
 */
async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		// Verify authentication
		const authHeader = request.headers.get('authorization');
		if (!authHeader?.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		const token = authHeader.substring(7);
		await verifyToken(token);

		// TODO: Implement actual model selection statistics
		// For now, return mock data to prevent API errors
		const modelStats = {
			totalRequests: 0,
			modelUsage: {},
			averageResponseTime: 0,
			successRate: 0,
			costByModel: {},
			qualityScores: {},
			selectionCriteria: {
				cost: 0,
				speed: 0,
				quality: 0,
			},
		};

		return NextResponse.json({
			success: true,
			data: modelStats,
		});
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ success: false, error: error.message }, { status: 401 });
		}

		console.error('Error getting model stats:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 }
		);
	}
}

const wrappedGET = withErrorHandling(GET);
export { wrappedGET as GET };
