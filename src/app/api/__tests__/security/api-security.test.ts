import { jest, describe, beforeEach, it, expect } from '@jest/globals';
import { NextRequest } from 'next/server';
import { GET as collectionsGET, POST as collectionsPOST } from '../../collections/route';
import { POST as generateParagraphPOST } from '../../llm/generate-paragraph/route';
import { Language } from '@prisma/client';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/backend/wire');

const mockAuth = require('@/lib/auth').auth;
const mockGetCollectionService = require('@/backend/wire').getCollectionService;
const mockGetLLMService = require('@/backend/wire').getLLMService;

const mockCollectionService = {
	getUserCollections: jest.fn(),
	createCollection: jest.fn(),
};

const mockLLMService = {
	generateParagraph: jest.fn(),
};

describe('API Security Tests', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		mockGetCollectionService.mockReturnValue(mockCollectionService);
		mockGetLLMService.mockResolvedValue(mockLLMService);
	});

	describe('Authentication Security', () => {
		it('should reject requests without authentication', async () => {
			mockAuth.mockResolvedValue(null); // No authenticated user

			const request = new NextRequest('http://localhost:3000/api/collections');
			const response = await collectionsGET(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toBe('Unauthorized');
		});

		it('should reject requests with invalid JWT tokens', async () => {
			mockAuth.mockRejectedValue(new Error('Invalid token'));

			const request = new NextRequest('http://localhost:3000/api/collections', {
				headers: {
					Authorization: 'Bearer invalid-jwt-token',
				},
			});

			const response = await collectionsGET(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toBe('Unauthorized');
		});

		it('should reject requests with expired tokens', async () => {
			mockAuth.mockRejectedValue(new Error('Token expired'));

			const request = new NextRequest('http://localhost:3000/api/collections', {
				headers: {
					Authorization: 'Bearer expired-jwt-token',
				},
			});

			const response = await collectionsGET(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toBe('Unauthorized');
		});

		it('should validate user permissions for protected resources', async () => {
			const mockUser = { id: 'user-1', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser });

			// Mock service to return collections for different user
			mockCollectionService.getUserCollections.mockResolvedValue([
				{
					id: 'collection-1',
					name: 'Other User Collection',
					user_id: 'user-2', // Different user
					target_language: Language.EN,
					source_language: Language.VI,
				},
			]);

			const request = new NextRequest('http://localhost:3000/api/collections');
			const response = await collectionsGET(request);

			expect(response.status).toBe(200);
			// Should only return collections for authenticated user
			expect(mockCollectionService.getUserCollections).toHaveBeenCalledWith('user-1');
		});
	});

	describe('Input Validation Security', () => {
		it('should prevent SQL injection in collection names', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser });

			const maliciousPayload = {
				name: "'; DROP TABLE collections; --",
				target_language: Language.EN,
				source_language: Language.VI,
			};

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(maliciousPayload),
			});

			const response = await collectionsPOST(request);
			const data = await response.json();

			// Should either validate and reject, or sanitize the input
			if (response.status === 400) {
				expect(data.error).toContain('Invalid');
			} else if (response.status === 200) {
				// If accepted, should be sanitized
				expect(mockCollectionService.createCollection).toHaveBeenCalledWith(
					expect.any(String),
					expect.stringMatching(/^[^';]*$/), // Should not contain SQL injection characters
					expect.any(String),
					expect.any(String),
					expect.any(Array),
					expect.any(String),
					expect.any(String)
				);
			}
		});

		it('should prevent XSS attacks in collection names', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser });

			const xssPayload = {
				name: '<script>alert("XSS")</script>',
				target_language: Language.EN,
				source_language: Language.VI,
			};

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(xssPayload),
			});

			const response = await collectionsPOST(request);
			const data = await response.json();

			// Should either validate and reject, or sanitize the input
			if (response.status === 400) {
				expect(data.error).toContain('Invalid');
			} else if (response.status === 200) {
				// If accepted, should be sanitized
				expect(mockCollectionService.createCollection).toHaveBeenCalledWith(
					expect.any(String),
					expect.not.stringMatching(/<script>/), // Should not contain script tags
					expect.any(String),
					expect.any(String),
					expect.any(Array),
					expect.any(String),
					expect.any(String)
				);
			}
		});

		it('should validate enum values strictly', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser });

			const invalidPayload = {
				name: 'Test Collection',
				target_language: 'INVALID_LANGUAGE',
				source_language: Language.VI,
			};

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(invalidPayload),
			});

			const response = await collectionsPOST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Invalid');
		});

		it('should prevent oversized payloads', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser });

			const oversizedPayload = {
				name: 'A'.repeat(10000), // Very long name
				target_language: Language.EN,
				source_language: Language.VI,
			};

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(oversizedPayload),
			});

			const response = await collectionsPOST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('too long');
		});
	});

	describe('Rate Limiting Security', () => {
		it('should implement rate limiting for expensive operations', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser });
			mockLLMService.generateParagraph.mockResolvedValue(['Generated paragraph']);

			const requests = [];

			// Make many rapid requests
			for (let i = 0; i < 20; i++) {
				const request = new NextRequest(
					'http://localhost:3000/api/llm/generate-paragraph',
					{
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
							'X-Forwarded-For': '127.0.0.1',
						},
						body: JSON.stringify({
							keywords: ['test'],
							language: Language.EN,
							difficulty: 'INTERMEDIATE',
							count: 1,
						}),
					}
				);
				requests.push(generateParagraphPOST(request));
			}

			const responses = await Promise.all(requests);

			// Some requests should be rate limited
			const rateLimitedResponses = responses.filter((response) => response.status === 429);
			const successfulResponses = responses.filter((response) => response.status === 200);

			// Should have some rate limiting in place
			expect(rateLimitedResponses.length).toBeGreaterThan(0);
			expect(successfulResponses.length).toBeLessThan(20);
		});
	});

	describe('Data Exposure Security', () => {
		it('should not expose sensitive information in error messages', async () => {
			mockAuth.mockRejectedValue(new Error('Database connection failed: password=secret123'));

			const request = new NextRequest('http://localhost:3000/api/collections');
			const response = await collectionsGET(request);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toBe('Unauthorized');
			// Should not expose internal error details
			expect(JSON.stringify(data)).not.toContain('password');
			expect(JSON.stringify(data)).not.toContain('secret');
		});

		it('should not expose user data across user boundaries', async () => {
			const mockUser1 = { id: 'user-1', email: '<EMAIL>' };
			const mockUser2 = { id: 'user-2', email: '<EMAIL>' };

			// First request as user 1
			mockAuth.mockResolvedValueOnce({ user: mockUser1 });
			mockCollectionService.getUserCollections.mockResolvedValueOnce([
				{ id: 'collection-1', name: 'User 1 Collection', user_id: 'user-1' },
			]);

			const request1 = new NextRequest('http://localhost:3000/api/collections');
			const response1 = await collectionsGET(request1);
			const data1 = await response1.json();

			// Second request as user 2
			mockAuth.mockResolvedValueOnce({ user: mockUser2 });
			mockCollectionService.getUserCollections.mockResolvedValueOnce([
				{ id: 'collection-2', name: 'User 2 Collection', user_id: 'user-2' },
			]);

			const request2 = new NextRequest('http://localhost:3000/api/collections');
			const response2 = await collectionsGET(request2);
			const data2 = await response2.json();

			// Each user should only see their own data
			expect(data1[0].user_id).toBe('user-1');
			expect(data2[0].user_id).toBe('user-2');
			expect(data1[0].id).not.toBe(data2[0].id);
		});
	});

	describe('CORS Security', () => {
		it('should implement proper CORS headers', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser });
			mockCollectionService.getUserCollections.mockResolvedValue([]);

			const request = new NextRequest('http://localhost:3000/api/collections', {
				headers: {
					Origin: 'https://malicious-site.com',
				},
			});

			const response = await collectionsGET(request);

			// Should not allow arbitrary origins
			const corsHeader = response.headers.get('Access-Control-Allow-Origin');
			expect(corsHeader).not.toBe('*');
			expect(corsHeader).not.toBe('https://malicious-site.com');
		});
	});

	describe('Content Security', () => {
		it('should validate content types strictly', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser });

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'text/plain' }, // Wrong content type
				body: 'not json',
			});

			const response = await collectionsPOST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Invalid');
		});

		it('should prevent malformed JSON attacks', async () => {
			const mockUser = { id: 'test-user-id', email: '<EMAIL>' };
			mockAuth.mockResolvedValue({ user: mockUser });

			const request = new NextRequest('http://localhost:3000/api/collections', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: '{"name": "test", "target_language": "EN", "source_language": "VI"', // Malformed JSON
			});

			const response = await collectionsPOST(request);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toContain('Invalid JSON');
		});
	});
});
