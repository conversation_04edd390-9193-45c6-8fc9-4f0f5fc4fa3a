import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReviewClient } from '../review-client';
import { CollectionsContext, AuthContext, TranslationContext } from '@/contexts';
import { Language } from '@prisma/client';
import { mockCollection, mockWordDetail, mockUser } from '@/test/fixtures';

// Mock Next.js navigation
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
	useRouter: () => ({
		push: mockPush,
		replace: jest.fn(),
		back: jest.fn(),
		forward: jest.fn(),
		refresh: jest.fn(),
		prefetch: jest.fn(),
	}),
	useParams: () => ({ id: 'test-collection-id' }),
}));

// Mock fetch
global.fetch = jest.fn();

// Mock components
jest.mock('@/components/ui/loading-spinner', () => ({
	LoadingSpinner: () => <div data-testid="loading-spinner">Loading...</div>,
}));

jest.mock('@/components/ui/button', () => ({
	Button: ({ children, onClick, disabled, ...props }: any) => (
		<button onClick={onClick} disabled={disabled} {...props}>
			{children}
		</button>
	),
}));

jest.mock('@/components/ui/word-detail-card', () => ({
	WordDetailCard: ({ word, ...props }: any) => (
		<div data-testid="word-detail-card">
			<h3>{word.term}</h3>
			<p>{word.definitions[0]?.explains[0]?.EN}</p>
		</div>
	),
}));

const mockCollectionsContext = {
	collections: [],
	setCollections: jest.fn(),
	currentCollection: mockCollection({ id: 'test-collection-id' }),
	setCurrentCollection: jest.fn(),
	currentCollectionWords: [],
	setCurrentCollectionWords: jest.fn(),
	isLoading: false,
	setIsLoading: jest.fn(),
	error: null,
	setError: jest.fn(),
	fetchCollections: jest.fn(),
	createCollection: jest.fn(),
	updateCollection: jest.fn(),
	deleteCollection: jest.fn(),
	addWordsToCollection: jest.fn(),
	addTermToCollection: jest.fn(),
	removeWordsFromCollection: jest.fn(),
	fetchWordsByCollection: jest.fn(),
	clearCurrentCollection: jest.fn(),
	addTermToCurrentCollection: jest.fn(),
	addWordsToCurrentCollection: jest.fn(),
};

const mockAuthContext = {
	user: mockUser(),
	login: jest.fn(),
	logout: jest.fn(),
	isLoading: false,
	error: null,
};

const mockTranslationContext = {
	language: Language.EN,
	setLanguage: jest.fn(),
	t: jest.fn((key: string) => key),
	isLoading: false,
};

const AllProviders = ({ children }: { children: React.ReactNode }) => (
	<AuthContext.Provider value={mockAuthContext}>
		<TranslationContext.Provider value={mockTranslationContext}>
			<CollectionsContext.Provider value={mockCollectionsContext}>
				{children}
			</CollectionsContext.Provider>
		</TranslationContext.Provider>
	</AuthContext.Provider>
);

describe('ReviewClient', () => {
	const user = userEvent.setup();

	beforeEach(() => {
		jest.clearAllMocks();
		(fetch as jest.Mock).mockClear();
		mockPush.mockClear();
	});

	it('should render review interface with words', async () => {
		const mockWords = [
			mockWordDetail({ term: 'test', definitions: [{ explains: [{ EN: 'A test' }] }] }),
			mockWordDetail({ term: 'example', definitions: [{ explains: [{ EN: 'An example' }] }] }),
		];

		(fetch as jest.Mock).mockResolvedValueOnce({
			ok: true,
			json: async () => mockWords,
		});

		render(
			<AllProviders>
				<ReviewClient />
			</AllProviders>
		);

		await waitFor(() => {
			expect(screen.getByText('review.title')).toBeInTheDocument();
		});

		// Should show first word
		expect(screen.getByText('test')).toBeInTheDocument();
		expect(screen.getByText('A test')).toBeInTheDocument();
	});

	it('should handle empty words list', async () => {
		(fetch as jest.Mock).mockResolvedValueOnce({
			ok: true,
			json: async () => [],
		});

		render(
			<AllProviders>
				<ReviewClient />
			</AllProviders>
		);

		await waitFor(() => {
			expect(screen.getByText('review.no_words_to_review')).toBeInTheDocument();
		});

		expect(screen.getByText('review.generate_words_btn')).toBeInTheDocument();
	});

	it('should navigate to next word when mark as seen', async () => {
		const mockWords = [
			mockWordDetail({ id: 'word1', term: 'test' }),
			mockWordDetail({ id: 'word2', term: 'example' }),
		];

		(fetch as jest.Mock)
			.mockResolvedValueOnce({
				ok: true,
				json: async () => mockWords,
			})
			.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ success: true }),
			});

		render(
			<AllProviders>
				<ReviewClient />
			</AllProviders>
		);

		await waitFor(() => {
			expect(screen.getByText('test')).toBeInTheDocument();
		});

		// Mark first word as seen
		const markSeenButton = screen.getByText('review.mark_seen_next_btn');
		await user.click(markSeenButton);

		await waitFor(() => {
			expect(fetch).toHaveBeenCalledWith('/api/last-seen-word', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ wordId: 'word1' }),
			});
		});

		// Should show second word
		await waitFor(() => {
			expect(screen.getByText('example')).toBeInTheDocument();
		});
	});

	it('should complete review session when all words are reviewed', async () => {
		const mockWords = [
			mockWordDetail({ id: 'word1', term: 'test' }),
		];

		(fetch as jest.Mock)
			.mockResolvedValueOnce({
				ok: true,
				json: async () => mockWords,
			})
			.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ success: true }),
			});

		render(
			<AllProviders>
				<ReviewClient />
			</AllProviders>
		);

		await waitFor(() => {
			expect(screen.getByText('test')).toBeInTheDocument();
		});

		// Mark the only word as seen
		const markSeenButton = screen.getByText('review.mark_seen_next_btn');
		await user.click(markSeenButton);

		await waitFor(() => {
			expect(screen.getByText('review.session_complete_title')).toBeInTheDocument();
		});

		expect(screen.getByText('review.restart_review_btn')).toBeInTheDocument();
		expect(screen.getByText('review.back_to_collection_btn')).toBeInTheDocument();
	});

	it('should handle mark as seen error', async () => {
		const mockWords = [
			mockWordDetail({ id: 'word1', term: 'test' }),
		];

		(fetch as jest.Mock)
			.mockResolvedValueOnce({
				ok: true,
				json: async () => mockWords,
			})
			.mockResolvedValueOnce({
				ok: false,
				json: async () => ({ error: 'Failed to mark word as seen' }),
			});

		render(
			<AllProviders>
				<ReviewClient />
			</AllProviders>
		);

		await waitFor(() => {
			expect(screen.getByText('test')).toBeInTheDocument();
		});

		// Try to mark word as seen
		const markSeenButton = screen.getByText('review.mark_seen_next_btn');
		await user.click(markSeenButton);

		await waitFor(() => {
			expect(screen.getByText('review.mark_seen_error_title')).toBeInTheDocument();
		});
	});

	it('should restart review session', async () => {
		const mockWords = [
			mockWordDetail({ id: 'word1', term: 'test' }),
		];

		(fetch as jest.Mock)
			.mockResolvedValueOnce({
				ok: true,
				json: async () => mockWords,
			})
			.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ success: true }),
			})
			.mockResolvedValueOnce({
				ok: true,
				json: async () => mockWords,
			});

		render(
			<AllProviders>
				<ReviewClient />
			</AllProviders>
		);

		// Complete review session
		await waitFor(() => {
			expect(screen.getByText('test')).toBeInTheDocument();
		});

		const markSeenButton = screen.getByText('review.mark_seen_next_btn');
		await user.click(markSeenButton);

		await waitFor(() => {
			expect(screen.getByText('review.session_complete_title')).toBeInTheDocument();
		});

		// Restart review
		const restartButton = screen.getByText('review.restart_review_btn');
		await user.click(restartButton);

		await waitFor(() => {
			expect(screen.getByText('test')).toBeInTheDocument();
		});
	});

	it('should navigate to word generation', async () => {
		(fetch as jest.Mock).mockResolvedValueOnce({
			ok: true,
			json: async () => [],
		});

		render(
			<AllProviders>
				<ReviewClient />
			</AllProviders>
		);

		await waitFor(() => {
			expect(screen.getByText('review.no_words_to_review')).toBeInTheDocument();
		});

		const generateWordsButton = screen.getByText('review.generate_words_btn');
		await user.click(generateWordsButton);

		expect(mockPush).toHaveBeenCalledWith('/collections/test-collection-id/vocabulary/generate');
	});

	it('should navigate back to collection', async () => {
		const mockWords = [
			mockWordDetail({ id: 'word1', term: 'test' }),
		];

		(fetch as jest.Mock)
			.mockResolvedValueOnce({
				ok: true,
				json: async () => mockWords,
			})
			.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ success: true }),
			});

		render(
			<AllProviders>
				<ReviewClient />
			</AllProviders>
		);

		// Complete review session
		await waitFor(() => {
			expect(screen.getByText('test')).toBeInTheDocument();
		});

		const markSeenButton = screen.getByText('review.mark_seen_next_btn');
		await user.click(markSeenButton);

		await waitFor(() => {
			expect(screen.getByText('review.session_complete_title')).toBeInTheDocument();
		});

		// Go back to collection
		const backButton = screen.getByText('review.back_to_collection_btn');
		await user.click(backButton);

		expect(mockPush).toHaveBeenCalledWith('/collections/test-collection-id');
	});

	it('should show loading state while marking word as seen', async () => {
		const mockWords = [
			mockWordDetail({ id: 'word1', term: 'test' }),
		];

		(fetch as jest.Mock)
			.mockResolvedValueOnce({
				ok: true,
				json: async () => mockWords,
			})
			.mockImplementationOnce(() => new Promise(resolve => setTimeout(resolve, 1000)));

		render(
			<AllProviders>
				<ReviewClient />
			</AllProviders>
		);

		await waitFor(() => {
			expect(screen.getByText('test')).toBeInTheDocument();
		});

		// Click mark as seen button
		const markSeenButton = screen.getByText('review.mark_seen_next_btn');
		await user.click(markSeenButton);

		// Should show loading spinner
		expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
	});

	it('should handle fetch words error', async () => {
		(fetch as jest.Mock).mockResolvedValueOnce({
			ok: false,
			json: async () => ({ error: 'Failed to fetch words' }),
		});

		render(
			<AllProviders>
				<ReviewClient />
			</AllProviders>
		);

		await waitFor(() => {
			expect(screen.getByText('review.error_loading_words')).toBeInTheDocument();
		});
	});
});
