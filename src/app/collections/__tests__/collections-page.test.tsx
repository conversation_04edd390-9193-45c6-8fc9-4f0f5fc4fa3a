import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CollectionsPage } from '../page';
import { CollectionsContext, AuthContext, TranslationContext } from '@/contexts';
import { Language } from '@prisma/client';
import { mockCollection, mockUser } from '@/test/fixtures';

// Mock Next.js navigation
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
	useRouter: () => ({
		push: mockPush,
		replace: jest.fn(),
		back: jest.fn(),
		forward: jest.fn(),
		refresh: jest.fn(),
		prefetch: jest.fn(),
	}),
	useSearchParams: () => new URLSearchParams(),
	usePathname: () => '/collections',
}));

// Mock components
jest.mock('@/components/ui/loading-spinner', () => ({
	LoadingSpinner: () => <div data-testid="loading-spinner">Loading...</div>,
}));

jest.mock('@/components/ui/button', () => ({
	Button: ({ children, onClick, disabled, ...props }: any) => (
		<button onClick={onClick} disabled={disabled} {...props}>
			{children}
		</button>
	),
}));

const mockCollectionsContext = {
	collections: [],
	setCollections: jest.fn(),
	currentCollection: null,
	setCurrentCollection: jest.fn(),
	currentCollectionWords: [],
	setCurrentCollectionWords: jest.fn(),
	isLoading: false,
	setIsLoading: jest.fn(),
	error: null,
	setError: jest.fn(),
	fetchCollections: jest.fn(),
	createCollection: jest.fn(),
	updateCollection: jest.fn(),
	deleteCollection: jest.fn(),
	addWordsToCollection: jest.fn(),
	addTermToCollection: jest.fn(),
	removeWordsFromCollection: jest.fn(),
	fetchWordsByCollection: jest.fn(),
	clearCurrentCollection: jest.fn(),
	addTermToCurrentCollection: jest.fn(),
	addWordsToCurrentCollection: jest.fn(),
};

const mockAuthContext = {
	user: mockUser(),
	login: jest.fn(),
	logout: jest.fn(),
	isLoading: false,
	error: null,
};

const mockTranslationContext = {
	language: Language.EN,
	setLanguage: jest.fn(),
	t: jest.fn((key: string) => key),
	isLoading: false,
};

const AllProviders = ({ children }: { children: React.ReactNode }) => (
	<AuthContext.Provider value={mockAuthContext}>
		<TranslationContext.Provider value={mockTranslationContext}>
			<CollectionsContext.Provider value={mockCollectionsContext}>
				{children}
			</CollectionsContext.Provider>
		</TranslationContext.Provider>
	</AuthContext.Provider>
);

describe('CollectionsPage', () => {
	const user = userEvent.setup();

	beforeEach(() => {
		jest.clearAllMocks();
		mockPush.mockClear();
	});

	it('should render collections page with empty state', () => {
		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		expect(screen.getByText('collections.title')).toBeInTheDocument();
		expect(screen.getByText('collections.create_new')).toBeInTheDocument();
		expect(screen.getByText('collections.empty_state')).toBeInTheDocument();
	});

	it('should render collections list when collections exist', () => {
		const mockCollections = [
			mockCollection({ name: 'Collection 1', word_ids: ['word1', 'word2'] }),
			mockCollection({ name: 'Collection 2', word_ids: ['word3'] }),
		];

		mockCollectionsContext.collections = mockCollections;

		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		expect(screen.getByText('Collection 1')).toBeInTheDocument();
		expect(screen.getByText('Collection 2')).toBeInTheDocument();
		expect(screen.getByText('2 collections.words')).toBeInTheDocument();
		expect(screen.getByText('1 collections.words')).toBeInTheDocument();
	});

	it('should show loading state', () => {
		mockCollectionsContext.isLoading = true;

		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
	});

	it('should show error state', () => {
		mockCollectionsContext.error = new Error('Failed to load collections');

		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		expect(screen.getByText('collections.error_loading')).toBeInTheDocument();
		expect(screen.getByText('collections.retry')).toBeInTheDocument();
	});

	it('should handle retry on error', async () => {
		mockCollectionsContext.error = new Error('Failed to load collections');

		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		const retryButton = screen.getByText('collections.retry');
		await user.click(retryButton);

		expect(mockCollectionsContext.fetchCollections).toHaveBeenCalled();
	});

	it('should navigate to collection detail when clicked', async () => {
		const mockCollections = [
			mockCollection({ id: 'collection-1', name: 'Collection 1' }),
		];

		mockCollectionsContext.collections = mockCollections;

		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		const collectionCard = screen.getByText('Collection 1');
		await user.click(collectionCard);

		expect(mockPush).toHaveBeenCalledWith('/collections/collection-1');
	});

	it('should open create collection modal', async () => {
		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		const createButton = screen.getByText('collections.create_new');
		await user.click(createButton);

		expect(screen.getByText('collections.create_title')).toBeInTheDocument();
		expect(screen.getByLabelText('collections.name_label')).toBeInTheDocument();
		expect(screen.getByLabelText('collections.target_language_label')).toBeInTheDocument();
		expect(screen.getByLabelText('collections.source_language_label')).toBeInTheDocument();
	});

	it('should create new collection', async () => {
		const mockCreatedCollection = mockCollection({
			name: 'New Collection',
			target_language: Language.EN,
			source_language: Language.VI,
		});

		mockCollectionsContext.createCollection.mockResolvedValue(mockCreatedCollection);

		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		// Open create modal
		const createButton = screen.getByText('collections.create_new');
		await user.click(createButton);

		// Fill form
		const nameInput = screen.getByLabelText('collections.name_label');
		await user.type(nameInput, 'New Collection');

		const targetLanguageSelect = screen.getByLabelText('collections.target_language_label');
		await user.selectOptions(targetLanguageSelect, Language.EN);

		const sourceLanguageSelect = screen.getByLabelText('collections.source_language_label');
		await user.selectOptions(sourceLanguageSelect, Language.VI);

		// Submit form
		const submitButton = screen.getByText('collections.create');
		await user.click(submitButton);

		await waitFor(() => {
			expect(mockCollectionsContext.createCollection).toHaveBeenCalledWith(
				'New Collection',
				Language.EN,
				Language.VI,
				undefined
			);
		});
	});

	it('should validate form fields', async () => {
		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		// Open create modal
		const createButton = screen.getByText('collections.create_new');
		await user.click(createButton);

		// Try to submit without filling required fields
		const submitButton = screen.getByText('collections.create');
		await user.click(submitButton);

		expect(screen.getByText('collections.name_required')).toBeInTheDocument();
	});

	it('should handle create collection error', async () => {
		mockCollectionsContext.createCollection.mockRejectedValue(
			new Error('Collection name already exists')
		);

		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		// Open create modal
		const createButton = screen.getByText('collections.create_new');
		await user.click(createButton);

		// Fill form
		const nameInput = screen.getByLabelText('collections.name_label');
		await user.type(nameInput, 'Duplicate Collection');

		const targetLanguageSelect = screen.getByLabelText('collections.target_language_label');
		await user.selectOptions(targetLanguageSelect, Language.EN);

		const sourceLanguageSelect = screen.getByLabelText('collections.source_language_label');
		await user.selectOptions(sourceLanguageSelect, Language.VI);

		// Submit form
		const submitButton = screen.getByText('collections.create');
		await user.click(submitButton);

		await waitFor(() => {
			expect(screen.getByText('Collection name already exists')).toBeInTheDocument();
		});
	});

	it('should close create modal on cancel', async () => {
		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		// Open create modal
		const createButton = screen.getByText('collections.create_new');
		await user.click(createButton);

		expect(screen.getByText('collections.create_title')).toBeInTheDocument();

		// Cancel
		const cancelButton = screen.getByText('collections.cancel');
		await user.click(cancelButton);

		expect(screen.queryByText('collections.create_title')).not.toBeInTheDocument();
	});

	it('should filter collections by search term', async () => {
		const mockCollections = [
			mockCollection({ name: 'English Vocabulary' }),
			mockCollection({ name: 'Spanish Grammar' }),
			mockCollection({ name: 'French Phrases' }),
		];

		mockCollectionsContext.collections = mockCollections;

		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		// All collections should be visible initially
		expect(screen.getByText('English Vocabulary')).toBeInTheDocument();
		expect(screen.getByText('Spanish Grammar')).toBeInTheDocument();
		expect(screen.getByText('French Phrases')).toBeInTheDocument();

		// Search for "English"
		const searchInput = screen.getByPlaceholderText('collections.search_placeholder');
		await user.type(searchInput, 'English');

		// Only English collection should be visible
		expect(screen.getByText('English Vocabulary')).toBeInTheDocument();
		expect(screen.queryByText('Spanish Grammar')).not.toBeInTheDocument();
		expect(screen.queryByText('French Phrases')).not.toBeInTheDocument();
	});

	it('should show no results message when search has no matches', async () => {
		const mockCollections = [
			mockCollection({ name: 'English Vocabulary' }),
		];

		mockCollectionsContext.collections = mockCollections;

		render(
			<AllProviders>
				<CollectionsPage />
			</AllProviders>
		);

		// Search for non-existent term
		const searchInput = screen.getByPlaceholderText('collections.search_placeholder');
		await user.type(searchInput, 'NonExistent');

		expect(screen.getByText('collections.no_results')).toBeInTheDocument();
	});
});
