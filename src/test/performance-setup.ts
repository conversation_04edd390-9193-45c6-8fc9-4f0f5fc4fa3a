import '@testing-library/jest-dom';

// Performance testing setup
console.log('🚀 Setting up performance testing environment...');

// Global performance tracking
global.performanceMetrics = {
	startTime: 0,
	endTime: 0,
	duration: 0,
	memoryUsage: {
		initial: 0,
		final: 0,
		peak: 0,
	},
};

// Performance measurement utilities
global.startPerformanceTest = (testName: string) => {
	console.log(`📊 Starting performance test: ${testName}`);
	global.performanceMetrics.startTime = Date.now();
	global.performanceMetrics.memoryUsage.initial = process.memoryUsage().heapUsed;
	
	// Track peak memory usage
	const memoryInterval = setInterval(() => {
		const currentMemory = process.memoryUsage().heapUsed;
		if (currentMemory > global.performanceMetrics.memoryUsage.peak) {
			global.performanceMetrics.memoryUsage.peak = currentMemory;
		}
	}, 100);
	
	global.performanceMetrics.memoryInterval = memoryInterval;
};

global.endPerformanceTest = (testName: string) => {
	global.performanceMetrics.endTime = Date.now();
	global.performanceMetrics.duration = global.performanceMetrics.endTime - global.performanceMetrics.startTime;
	global.performanceMetrics.memoryUsage.final = process.memoryUsage().heapUsed;
	
	if (global.performanceMetrics.memoryInterval) {
		clearInterval(global.performanceMetrics.memoryInterval);
	}
	
	const memoryIncrease = global.performanceMetrics.memoryUsage.final - global.performanceMetrics.memoryUsage.initial;
	const peakMemoryIncrease = global.performanceMetrics.memoryUsage.peak - global.performanceMetrics.memoryUsage.initial;
	
	console.log(`✅ Performance test completed: ${testName}`);
	console.log(`   Duration: ${global.performanceMetrics.duration}ms`);
	console.log(`   Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
	console.log(`   Peak memory increase: ${(peakMemoryIncrease / 1024 / 1024).toFixed(2)}MB`);
	
	return global.performanceMetrics;
};

// Performance assertions
global.expectPerformance = {
	toBeFasterThan: (maxDuration: number) => {
		expect(global.performanceMetrics.duration).toBeLessThan(maxDuration);
	},
	toUseMemoryLessThan: (maxMemoryMB: number) => {
		const memoryIncrease = global.performanceMetrics.memoryUsage.final - global.performanceMetrics.memoryUsage.initial;
		expect(memoryIncrease).toBeLessThan(maxMemoryMB * 1024 * 1024);
	},
	toPeakMemoryLessThan: (maxMemoryMB: number) => {
		const peakMemoryIncrease = global.performanceMetrics.memoryUsage.peak - global.performanceMetrics.memoryUsage.initial;
		expect(peakMemoryIncrease).toBeLessThan(maxMemoryMB * 1024 * 1024);
	},
};

// Mock heavy operations for performance testing
jest.mock('openai', () => ({
	__esModule: true,
	default: jest.fn().mockImplementation(() => ({
		chat: {
			completions: {
				create: jest.fn().mockImplementation(() => {
					// Simulate API delay for performance testing
					return new Promise(resolve => {
						setTimeout(() => {
							resolve({
								choices: [{
									message: {
										content: JSON.stringify({
											words: [{
												term: 'performance',
												language: 'EN',
												definitions: [{
													pos: ['noun'],
													ipa: '/pərˈfɔrməns/',
													explains: { EN: 'Performance definition', VI: 'Định nghĩa hiệu suất' },
													examples: { EN: 'Performance example', VI: 'Ví dụ hiệu suất' },
												}],
											}],
										}),
									},
								}],
								usage: { total_tokens: 100 },
							});
						}, Math.random() * 1000 + 500); // 500-1500ms delay
					});
				}),
			},
		},
	})),
}));

// Performance test environment variables
process.env.NODE_ENV = 'test';
process.env.PERFORMANCE_TEST = 'true';
process.env.LOG_LEVEL = 'error'; // Reduce logging noise
process.env.DISABLE_CACHE = 'false'; // Test with caching enabled
process.env.MOCK_LLM_RESPONSES = 'false'; // Use real mock delays

// Increase test timeout for performance tests
jest.setTimeout(60000);

// Global cleanup
afterEach(() => {
	if (global.performanceMetrics.memoryInterval) {
		clearInterval(global.performanceMetrics.memoryInterval);
	}
	
	// Force garbage collection if available
	if (global.gc) {
		global.gc();
	}
});

console.log('✅ Performance testing environment ready');

// Type declarations for global performance utilities
declare global {
	var performanceMetrics: {
		startTime: number;
		endTime: number;
		duration: number;
		memoryUsage: {
			initial: number;
			final: number;
			peak: number;
		};
		memoryInterval?: NodeJS.Timeout;
	};
	
	var startPerformanceTest: (testName: string) => void;
	var endPerformanceTest: (testName: string) => typeof performanceMetrics;
	
	var expectPerformance: {
		toBeFasterThan: (maxDuration: number) => void;
		toUseMemoryLessThan: (maxMemoryMB: number) => void;
		toPeakMemoryLessThan: (maxMemoryMB: number) => void;
	};
}
