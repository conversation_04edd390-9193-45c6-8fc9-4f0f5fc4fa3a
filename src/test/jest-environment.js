const { TestEnvironment } = require('jest-environment-node');
const { fetch, Headers, Request, Response } = require('undici');
const { TextEncoder, TextDecoder } = require('node:util');

class CustomTestEnvironment extends TestEnvironment {
	constructor(config, context) {
		super(config, context);
	}

	async setup() {
		await super.setup();
		
		// Add polyfills to global environment
		this.global.fetch = fetch;
		this.global.Headers = Headers;
		this.global.Request = Request;
		this.global.Response = Response;
		this.global.TextEncoder = TextEncoder;
		this.global.TextDecoder = TextDecoder;
	}

	async teardown() {
		await super.teardown();
	}
}

module.exports = CustomTestEnvironment;
