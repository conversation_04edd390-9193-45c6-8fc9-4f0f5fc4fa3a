# Test Environment Configuration
NODE_ENV=test

# Database Configuration for Testing
DATABASE_URL="postgresql://postgres:password@localhost:5432/vocab_test?schema=public"
MONGODB_URI="mongodb://localhost:27017/vocab_test"

# JWT Configuration
JWT_SECRET="test-jwt-secret-key-for-testing-only"
JWT_EXPIRES_IN="1h"

# LLM Configuration (Mock/Test Keys)
LLM_OPENAI_API_KEY="test-openai-key"
LLM_GEMINI_API_KEY="test-gemini-key"
LLM_DEFAULT_PROVIDER="openai"
LLM_OPENAI_MODEL="gpt-4o-mini"
LLM_GEMINI_MODEL="gemini-1.5-flash"

# Feature Flags for Testing
FEATURE_MONGODB_ENABLED=false
FEATURE_GOOGLE_LOGIN=false
FEATURE_SEMANTIC_CACHE=false
FEATURE_REDIS_CACHE=false

# Cache Configuration
CACHE_TTL_VOCABULARY=300
CACHE_TTL_WORD_DETAILS=300
CACHE_TTL_PARAGRAPHS=300
CACHE_TTL_QUESTIONS=300
CACHE_TTL_EVALUATIONS=300
CACHE_TTL_GRAMMAR_PRACTICE=300

# Redis Configuration (for testing)
REDIS_URL="redis://localhost:6379/1"

# Test-specific Configuration
TEST_TIMEOUT=10000
TEST_COVERAGE_THRESHOLD=70
TEST_PARALLEL_WORKERS=1

# Disable external services in tests
DISABLE_EXTERNAL_APIS=true
MOCK_LLM_RESPONSES=true
MOCK_CACHE_RESPONSES=true

# Logging Configuration
LOG_LEVEL="error"
DISABLE_LOGGING=true

# Security Configuration
DISABLE_RATE_LIMITING=true
DISABLE_CSRF_PROTECTION=true
