# WordNet Database Integration

## Overview

This document describes the implementation of WordNet database integration in the Vocab application. The system now loads WordNet data directly into the database for improved performance and offline capability.

## Features

-   **Pre-loaded WordNet Data**: WordNet information is stored in the database for fast access
-   **Database-First Approach**: Vocabulary lookup prioritizes database data over real-time API calls
-   **Batch Loading**: Efficient scripts to load WordNet data in batches
-   **Fallback Mechanism**: Falls back to real-time WordNet lookup if data not found in database
-   **Performance Optimized**: Database indexes for fast search and retrieval

## Architecture

### Database Schema

```sql
-- Word table with indexes for performance
model Word {
  id           String       @id() @default(uuid())
  term         String
  language     Language
  audio_url    String?
  definitions  Definition[]
  wordnet_data WordNetData?
  created_at   DateTime     @default(now())
  updated_at   DateTime     @updatedAt()

  @@unique([term, language])
  @@index([term])
  @@index([language])
  @@index([term, language])
}

-- WordNet data table
model WordNetData {
  id         String   @id() @default(uuid())
  word       Word     @relation(fields: [word_id], references: [id], onDelete: Cascade)
  word_id    String   @unique
  synsets    String[] // Synonym sets with definitions
  lemma      String?  // Base form of the word
  hypernyms  String[] // Broader terms (parent concepts)
  hyponyms   String[] // Narrower terms (child concepts)
  holonyms   String[] // Whole that contains this part
  meronyms   String[] // Parts that make up this whole
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt()

  @@index([word_id])
  @@index([lemma])
}
```

### Service Layer

#### WordNetService

-   `getWordNetInfo()`: Database-first approach with fallback to real-time lookup
-   `getWordNetInfoFromDatabase()`: Direct database query for WordNet data
-   `isWordNetAvailable()`: Check if WordNet library is available

#### WordService

-   `searchWords()`: Prioritizes words with WordNet data
-   `searchWordsWithWordNet()`: Search specifically for words with WordNet data

## Scripts

### Load WordNet Data

#### Complete WordNet Database (Recommended)

```bash
# Check WordNet database statistics
yarn load-wordnet-complete:stats
# Output: noun: 117,952 words, verb: 11,539 words, adj: 21,498 words, adv: 4,474 words

# Load all WordNet data (~155k words - takes 2-4 hours)
yarn load-wordnet-complete:all

# Load specific part of speech
yarn load-wordnet-complete:noun    # Load all nouns (~118k words)
yarn load-wordnet-complete:verb    # Load all verbs (~11k words)
yarn load-wordnet-complete:adj     # Load all adjectives (~21k words)
yarn load-wordnet-complete:adv     # Load all adverbs (~4k words)

# Test with limited words
yarn load-wordnet-complete:test    # 100 words, dry run
yarn load-wordnet-complete --pos noun --max-words 1000 --dry-run

# Load common English words (recommended for quick start)
yarn load-common-words
```

#### Legacy WordNet Loader (Limited)

```bash
# Legacy loader (only loads ~400 words due to natural library limitations)
yarn load-wordnet
yarn load-wordnet:test
yarn load-wordnet:noun
```

### Script Options

#### Complete WordNet Loader Options

```bash
# Complete WordNet loader options
--pos <pos>           # Load specific part of speech (noun, verb, adj, adv)
--batch-size <size>   # Batch size for processing (default: 500)
--max-words <count>   # Maximum words to process (for testing)
--start-from <num>    # Start from specific word number (for resuming)
--dry-run            # Run without saving to database
--stats              # Show WordNet database statistics
--help               # Show help message
```

#### Legacy WordNet Loader Options

```bash
# Legacy WordNet loader options (limited functionality)
--pos <pos>           # Load specific part of speech (noun, verb, adj, adv)
--batch-size <size>   # Batch size for processing (default: 100)
--max-words <count>   # Maximum words to process (for testing)
--dry-run            # Run without saving to database
--help               # Show help message
```

## Usage Examples

### Loading Data

#### Quick Start (Recommended)

```bash
# 1. Load common words first (80 essential words)
yarn load-common-words

# 2. Load sample data for testing (1000 nouns + 500 verbs)
yarn load-wordnet-complete --pos noun --max-words 1000
yarn load-wordnet-complete --pos verb --max-words 500

# 3. Check current database status
node -e "const {PrismaClient} = require('@prisma/client'); const p = new PrismaClient(); p.word.count().then(c => console.log('Total words:', c));"
```

#### Production Setup (Complete Database)

```bash
# Load complete WordNet database (~155k words)
# WARNING: This takes 2-4 hours and requires significant disk space

# Option 1: Load all at once
yarn load-wordnet-complete:all

# Option 2: Load by parts (recommended for better control)
yarn load-wordnet-complete:noun    # ~118k words, takes ~2 hours
yarn load-wordnet-complete:verb    # ~11k words, takes ~15 minutes
yarn load-wordnet-complete:adj     # ~21k words, takes ~30 minutes
yarn load-wordnet-complete:adv     # ~4k words, takes ~5 minutes
```

### API Usage

The vocabulary lookup API now automatically uses database data:

```typescript
// GET /api/words/search?term=computer
// Returns words with pre-loaded WordNet data

// GET /api/words?limit=20
// Returns words prioritizing those with WordNet data
```

### Frontend Integration

The vocabulary lookup page (`/vocabulary-lookup`) now:

-   Searches database-first for faster results
-   Displays WordNet information from pre-loaded data
-   Shows performance improvements in console logs

## Performance Benefits

### Before (Real-time WordNet)

-   ⏱️ 2-5 seconds per word lookup
-   🌐 Requires internet connection
-   💾 High memory usage during lookup
-   ❌ Fails if WordNet service unavailable

### After (Database Integration)

-   ⚡ <100ms per word lookup
-   🔌 Works offline
-   💾 Efficient database queries
-   ✅ Reliable with fallback mechanism

## Database Statistics

After loading common words and sample data:

-   **Total Words**: ~400 words
-   **Words with WordNet Data**: ~390 words
-   **Coverage**: 97.5% of loaded words have WordNet data

## Monitoring

### Check Database Status

```javascript
// Check word count and WordNet coverage
const wordCount = await prisma.word.count();
const wordNetCount = await prisma.wordNetData.count();
console.log(`Coverage: ${wordNetCount}/${wordCount} words have WordNet data`);
```

### Performance Monitoring

The system logs WordNet data usage:

```
✅ Found 15/20 words with WordNet data from database
```

## Troubleshooting

### Common Issues

1. **No WordNet data found**

    - Run `yarn load-common-words` to load basic vocabulary
    - Check if WordNet library is installed: `npm list wordnet-db`

2. **Slow performance**

    - Ensure database indexes are created: `yarn p:m`
    - Check database connection and query performance

3. **Script fails during loading**
    - Check database connection
    - Verify WordNet library installation
    - Use `--dry-run` flag to test without saving

### Debug Commands

```bash
# Test WordNet library
node -e "const natural = require('natural'); const wn = new natural.WordNet(); wn.lookup('test', console.log);"

# Check database connection
yarn p:s  # Open Prisma Studio

# Verify loaded data
node -e "const {PrismaClient} = require('@prisma/client'); const p = new PrismaClient(); p.word.count().then(console.log);"
```

## Future Enhancements

### Planned Features

-   **Full WordNet Loading**: Script to load complete WordNet database (~155k words)
-   **Incremental Updates**: Update WordNet data without full reload
-   **Advanced Relationships**: Better hypernym/hyponym relationship mapping
-   **Search Optimization**: Full-text search across WordNet definitions
-   **Caching Layer**: Redis caching for frequently accessed words

### Performance Optimizations

-   **Bulk Insert**: Optimize batch loading for large datasets
-   **Parallel Processing**: Multi-threaded WordNet data extraction
-   **Compression**: Compress WordNet data for storage efficiency
-   **Indexing**: Advanced database indexes for complex queries

## Migration Guide

### From Real-time to Database-first

1. **Load Initial Data**:

    ```bash
    yarn load-common-words
    ```

2. **Test Functionality**:

    - Visit `/vocabulary-lookup`
    - Search for common words like "house", "run", "good"
    - Verify WordNet data displays correctly

3. **Load More Data** (Optional):

    ```bash
    yarn load-wordnet --pos noun --max-words 1000
    ```

4. **Monitor Performance**:
    - Check console logs for database usage
    - Verify faster response times

### Rollback Plan

If issues occur, the system automatically falls back to real-time WordNet lookup when database data is unavailable.

## Conclusion

The WordNet database integration provides significant performance improvements while maintaining compatibility with the existing system. The database-first approach with fallback mechanism ensures reliability and improved user experience.

For questions or issues, refer to the troubleshooting section or check the implementation in:

-   `src/backend/services/wordnet.service.ts`
-   `src/backend/services/word.service.ts`
-   `scripts/load-wordnet.ts`
-   `scripts/load-common-words.ts`
