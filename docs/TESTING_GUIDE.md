# Testing Guide - Vocab App

## Overview

This document provides a comprehensive guide to the testing infrastructure implemented for the Vocab application. The testing strategy covers all major functionality including collections, stats, review, word generation, and paragraph practices with a target coverage of 70%.

## Testing Architecture

### Test Types

1. **Unit Tests** - Test individual functions and components in isolation
2. **Integration Tests** - Test API endpoints and service interactions
3. **Component Tests** - Test React components with user interactions
4. **E2E Tests** - Test complete user workflows across the application
5. **Performance Tests** - Test response times and resource usage
6. **Security Tests** - Test authentication, authorization, and input validation
7. **Load Tests** - Test system behavior under concurrent load

### Testing Tools

- **Jest** - Primary testing framework for unit and integration tests
- **Vitest** - Fast unit testing with HMR support
- **Playwright** - End-to-end browser testing
- **Testing Library** - React component testing utilities
- **MSW** - API mocking for tests
- **Supertest** - HTTP assertion library for API testing

## Test Structure

```
src/
├── backend/
│   ├── services/__tests__/
│   │   ├── collection.service.test.ts
│   │   ├── llm.service.test.ts
│   │   ├── collection-stats.service.test.ts
│   │   └── performance/
│   │       └── llm-performance.test.ts
│   └── repositories/__tests__/
│       └── collection.repository.test.ts
├── app/api/__tests__/
│   ├── collections/route.test.ts
│   ├── llm/generate-paragraph/route.test.ts
│   ├── load/api-load.test.ts
│   └── security/api-security.test.ts
├── hooks/__tests__/
│   └── use-collections.test.ts
├── app/collections/__tests__/
│   └── collections-page.test.tsx
└── test/
    ├── setup.ts
    ├── performance-setup.ts
    ├── fixtures/
    ├── mocks/
    └── helpers/
e2e/
├── collection-management.spec.ts
├── word-review-workflow.spec.ts
└── paragraph-practice-workflow.spec.ts
```

## Running Tests

### Quick Commands

```bash
# Run all unit tests
yarn test

# Run unit tests with coverage
yarn test:coverage

# Run Vitest unit tests
yarn test:unit

# Run E2E tests
yarn test:e2e

# Run performance tests
yarn test:performance

# Run security tests
yarn test:security

# Run all tests comprehensively
yarn test:all:comprehensive

# Setup test environment
yarn test:setup
```

### Test Scripts

```bash
# Run comprehensive test suite with report
tsx scripts/run-all-tests.ts

# Setup test database
tsx scripts/setup-test-db.ts
```

## Test Coverage

### Current Coverage Targets

- **Lines**: 70%
- **Functions**: 70%
- **Branches**: 70%
- **Statements**: 70%

### Coverage Reports

Coverage reports are generated in:
- `coverage/` - Jest coverage reports
- `coverage-vitest/` - Vitest coverage reports
- `test-results/` - Comprehensive test reports

## Key Test Areas

### 1. Collection Management

**Files Tested:**
- `src/backend/services/collection.service.ts`
- `src/backend/repositories/collection.repository.ts`
- `src/app/api/collections/route.ts`
- `src/app/collections/page.tsx`

**Test Coverage:**
- ✅ Create, read, update, delete collections
- ✅ Add/remove words from collections
- ✅ User authorization and data isolation
- ✅ Input validation and error handling
- ✅ Concurrent operations

### 2. LLM Integration

**Files Tested:**
- `src/backend/services/llm.service.ts`
- `src/app/api/llm/generate-paragraph/route.ts`

**Test Coverage:**
- ✅ Word detail generation
- ✅ Paragraph generation with different difficulties
- ✅ Q&A generation and evaluation
- ✅ Grammar practice generation
- ✅ Error handling and retry logic
- ✅ Performance optimization
- ✅ Token usage tracking

### 3. Stats and Analytics

**Files Tested:**
- `src/backend/services/collection-stats.service.ts`
- `src/app/api/collections/[id]/stats/route.ts`

**Test Coverage:**
- ✅ Word review tracking
- ✅ Practice session statistics
- ✅ Date range filtering
- ✅ Aggregation and reporting

### 4. Review System

**Files Tested:**
- `src/app/collections/[id]/vocabulary/review/review-client.tsx`

**Test Coverage:**
- ✅ Word review workflow
- ✅ Progress tracking
- ✅ Spaced repetition logic
- ✅ Session completion
- ✅ Error recovery

### 5. Paragraph Practice

**Files Tested:**
- Paragraph translation practice
- Q&A practice
- Grammar practice

**Test Coverage:**
- ✅ Keyword selection
- ✅ Content generation
- ✅ User interaction
- ✅ Evaluation and feedback
- ✅ Session management

## Performance Testing

### Benchmarks

- **Word Generation**: < 5 seconds for 3 words
- **Paragraph Generation**: < 10 seconds for 3 paragraphs
- **API Response Time**: < 500ms for simple queries
- **Memory Usage**: < 100MB baseline, < 500MB peak
- **Concurrent Users**: Support 50+ concurrent requests

### Performance Test Examples

```typescript
// Example performance test
it('should generate word details within 5 seconds', async () => {
  const startTime = Date.now();
  const result = await llmService.generateWordDetails(terms, Language.VI, Language.EN);
  const endTime = Date.now();
  const duration = endTime - startTime;

  expect(duration).toBeLessThan(5000);
  expect(result).toHaveLength(3);
});
```

## Security Testing

### Security Test Areas

- **Authentication**: JWT token validation, expiration
- **Authorization**: User data isolation, permission checks
- **Input Validation**: SQL injection, XSS prevention
- **Rate Limiting**: API abuse prevention
- **Data Exposure**: Sensitive information protection

### Security Test Examples

```typescript
// Example security test
it('should prevent SQL injection in collection names', async () => {
  const maliciousPayload = {
    name: "'; DROP TABLE collections; --",
    target_language: Language.EN,
    source_language: Language.VI,
  };

  const response = await POST(request);
  
  // Should either validate and reject, or sanitize
  expect(response.status).toBe(400);
});
```

## E2E Testing

### User Workflows Tested

1. **Collection Management**
   - Create, edit, delete collections
   - Search and filter collections
   - Navigate to collection details

2. **Word Review**
   - Start review session
   - Navigate through words
   - Mark words as seen
   - Complete session

3. **Paragraph Practice**
   - Generate paragraphs
   - Translate content
   - Receive evaluation
   - Practice Q&A and grammar

### E2E Test Examples

```typescript
// Example E2E test
test('should create a new collection', async ({ page }) => {
  await page.click('[data-testid="create-collection-btn"]');
  await page.fill('[data-testid="collection-name-input"]', 'Test Collection');
  await page.selectOption('[data-testid="target-language-select"]', 'EN');
  await page.click('[data-testid="create-collection-submit"]');
  
  await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
});
```

## Best Practices

### Writing Tests

1. **Use descriptive test names** that explain what is being tested
2. **Follow AAA pattern** - Arrange, Act, Assert
3. **Mock external dependencies** to ensure test isolation
4. **Test both success and error scenarios**
5. **Use data-testid attributes** for reliable element selection
6. **Keep tests focused** - one concept per test

### Test Data

1. **Use fixtures** for consistent test data
2. **Create factories** for generating test objects
3. **Clean up after tests** to prevent interference
4. **Use realistic data** that matches production scenarios

### Performance Considerations

1. **Run performance tests sequentially** to avoid interference
2. **Use appropriate timeouts** for different test types
3. **Monitor memory usage** during long-running tests
4. **Clean up resources** after each test

## Continuous Integration

### GitHub Actions

Tests are automatically run on:
- Pull requests
- Pushes to main branch
- Scheduled runs (daily)

### Test Reports

- Coverage reports uploaded to CI artifacts
- Performance metrics tracked over time
- Security scan results integrated
- E2E test videos for debugging failures

## Troubleshooting

### Common Issues

1. **Test timeouts** - Increase timeout for slow operations
2. **Memory leaks** - Ensure proper cleanup in afterEach
3. **Flaky tests** - Add proper waits and assertions
4. **Mock issues** - Verify mock implementations match real APIs

### Debugging

```bash
# Run tests in debug mode
yarn test --detectOpenHandles --forceExit

# Run specific test file
yarn test collection.service.test.ts

# Run tests with verbose output
yarn test --verbose

# Debug E2E tests
yarn test:e2e:debug
```

## Contributing

When adding new features:

1. **Write tests first** (TDD approach recommended)
2. **Ensure 70% coverage** for new code
3. **Add E2E tests** for new user workflows
4. **Update this documentation** for new test patterns
5. **Run full test suite** before submitting PR

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Vitest Documentation](https://vitest.dev/guide/)
- [Playwright Documentation](https://playwright.dev/docs/intro)
- [Testing Library Documentation](https://testing-library.com/docs/)
- [MSW Documentation](https://mswjs.io/docs/)

---

**Last Updated**: 2024-01-23
**Coverage Target**: 70%
**Test Files**: 50+ test files covering all major functionality
