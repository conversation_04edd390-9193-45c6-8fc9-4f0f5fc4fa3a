# WordNet Integration Documentation

## Overview

The Vocab app now includes comprehensive WordNet integration to provide rich semantic information for English vocabulary words. This integration enhances the learning experience by providing linguistic relationships, definitions, and semantic context.

## Features

### WordNet Data Types

The system captures and displays the following WordNet information:

- **Synsets**: Different meanings and definitions from WordNet database
- **Lemma**: Base or dictionary form of the word
- **Hypernyms**: More general terms that include this word (broader terms)
- **Hyponyms**: More specific terms that are types of this word (narrower terms)
- **Holonyms**: Things that this word is a part of (whole-part relations)
- **Meronyms**: Parts or components of this word (part-whole relations)

### Language Support

- **English (EN)**: Full WordNet integration with automatic data generation
- **Vietnamese (VI)**: No WordNet data (WordNet is English-only)

## Technical Implementation

### Backend Components

#### 1. Database Schema

```sql
-- New WordNetData model
model WordNetData {
  id         String   @id() @default(uuid())
  word       Word     @relation(fields: [word_id], references: [id], onDelete: Cascade)
  word_id    String   @unique
  synsets    String[] // Definitions from WordNet
  lemma      String?  // Base form of the word
  hypernyms  String[] // Broader terms
  hyponyms   String[] // Narrower terms
  holonyms   String[] // Whole-part relations
  meronyms   String[] // Part-whole relations
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt()
}

-- Updated Word model
model Word {
  // ... existing fields
  wordnet_data WordNetData?
}
```

#### 2. WordNet Service

- **Location**: `src/backend/services/wordnet.service.ts`
- **Main Methods**:
  - `getWordNetInfo(term, language)`: Extract WordNet data for a term
  - `isWordNetAvailable()`: Check if WordNet is available
- **Dependencies**: `natural` library with `wordnet-db`

#### 3. Word Service Integration

- Automatic WordNet data generation when creating English words
- Fallback gracefully if WordNet is unavailable
- No impact on word creation if WordNet fails

### Frontend Components

#### 1. WordNet Info Component

- **Location**: `src/components/wordnet/wordnet-info.tsx`
- **Features**:
  - Collapsible sections for each WordNet data type
  - Tooltips explaining each semantic relationship
  - Copy and search functionality for related terms
  - Responsive design with proper loading states

#### 2. WordNet Summary Component

- **Location**: `src/components/wordnet/wordnet-summary.tsx`
- **Features**:
  - Compact badges showing WordNet data availability
  - Tooltips with quick information
  - Used in word cards and listings

#### 3. Integration Points

- **Word Cards**: Show WordNet summary badges in headers
- **Word Detail Views**: Full WordNet information panels
- **Generate Words**: WordNet data included in new word creation

### Translations

- **Location**: `src/contexts/translations/wordnet.trans.ts`
- **Languages**: English and Vietnamese
- **Coverage**: All UI text, tooltips, and error messages

## Usage Examples

### Automatic Integration

When creating words through the LLM service or manually:

```typescript
// WordNet data is automatically added for English words
const word = await wordService.createWordWithRandomWordDetail({
  term: 'computer',
  language: Language.EN,
  definitions: [...],
  // wordnet_data will be automatically generated
});
```

### Manual WordNet Data

You can also provide WordNet data explicitly:

```typescript
const wordWithWordNet = await wordService.createWordWithRandomWordDetail({
  term: 'dog',
  language: Language.EN,
  definitions: [...],
  wordnet_data: {
    synsets: ['a domesticated carnivorous mammal'],
    lemma: 'dog',
    hypernyms: ['canine', 'mammal'],
    hyponyms: ['puppy', 'hound'],
    holonyms: [],
    meronyms: ['tail', 'paw'],
  },
});
```

### UI Components

```tsx
// Display full WordNet information
<WordNetInfo 
  wordNetData={word.wordnet_data} 
  term={word.term}
/>

// Display summary badges
<WordNetSummary wordNetData={word.wordnet_data} />
```

## Testing

### Backend Tests

- **WordNet Service**: `src/backend/services/__tests__/wordnet.service.test.ts`
- **Integration Tests**: `src/backend/services/__tests__/wordnet-integration.test.ts`
- **Test Scripts**: `scripts/test-wordnet.ts`, `scripts/test-word-creation.ts`

### UI Tests

- **Integration Test**: `scripts/test-ui-integration.ts`
- **Manual Testing**: Navigate to collections and verify WordNet data display

### Running Tests

```bash
# Test WordNet service
npx tsx scripts/test-wordnet.ts

# Test word creation with WordNet
npx tsx scripts/test-word-creation.ts

# Test UI integration
npx tsx scripts/test-ui-integration.ts
```

## Performance Considerations

### Caching

- WordNet lookups are cached using the existing cache service
- TTL: 7 days for vocabulary-related data
- Graceful degradation if WordNet is unavailable

### Error Handling

- WordNet failures don't block word creation
- Comprehensive error logging
- Fallback to empty WordNet data

### Optimization

- WordNet data is only generated for English words
- Lazy loading of WordNet information in UI
- Efficient database queries with proper indexing

## Troubleshooting

### Common Issues

1. **WordNet not available**: Check if `natural` and `wordnet-db` are installed
2. **No WordNet data**: Verify the word is in English and exists in WordNet
3. **UI not showing data**: Check that `wordnet_data` is included in database queries

### Debug Commands

```bash
# Check WordNet availability
npx tsx scripts/test-wordnet.ts

# Verify database schema
yarn p:s

# Check TypeScript compilation
yarn tsc --noEmit
```

## Future Enhancements

### Potential Improvements

1. **Enhanced Relationships**: More detailed hypernym/hyponym extraction
2. **Semantic Search**: Use WordNet data for improved word search
3. **Learning Paths**: Create learning sequences based on semantic relationships
4. **Multilingual Support**: Explore WordNet equivalents for other languages
5. **Visual Relationships**: Graph visualization of semantic relationships

### API Extensions

1. **WordNet Search**: Search words by semantic relationships
2. **Similarity Scoring**: Calculate semantic similarity between words
3. **Recommendation Engine**: Suggest related words for learning

## Dependencies

- **natural**: Natural language processing library
- **wordnet-db**: WordNet database for Node.js
- **@types/natural**: TypeScript definitions
- **@radix-ui/react-collapsible**: UI collapsible components
- **@radix-ui/react-tooltip**: UI tooltip components

## Conclusion

The WordNet integration provides a solid foundation for semantic vocabulary learning. The implementation is robust, well-tested, and designed for scalability. Users now have access to rich linguistic information that enhances their vocabulary learning experience.
