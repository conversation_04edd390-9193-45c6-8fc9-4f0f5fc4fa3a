# WordNet Setup Guide

This guide provides comprehensive instructions for downloading, setting up, and loading WordNet data into your vocabulary application database.

## Overview

WordNet is a large lexical database of English developed at Princeton University. It groups English words into sets of synonyms called synsets, provides short definitions and usage examples, and records a number of relations among these synonym sets or their members.

Our WordNet integration provides:
- **Complete WordNet 3.0 database** (~155,000 words)
- **Semantic relationships** (hypernyms, hyponyms, meronyms, holonyms)
- **Synset definitions** and glosses
- **Part-of-speech information**
- **Database integration** with PostgreSQL

## Quick Start

### 1. Install Dependencies

First, install all required dependencies for WordNet processing:

```bash
yarn install-wordnet-deps
```

This will install:
- `unzipper` - For extracting compressed files
- `natural` - Natural language processing library
- `wordnet-db` - Alternative WordNet data source
- TypeScript types for all packages

### 2. Quick Setup (Recommended for Testing)

For a quick start with 1000 common nouns:

```bash
yarn setup-wordnet:quick
```

This will:
- Download WordNet files (~10 MB)
- Extract and organize the data
- Load 1000 most common nouns into the database
- Takes approximately 5-10 minutes

### 3. Check Status

Verify the setup and see statistics:

```bash
yarn setup-wordnet:stats
```

## Complete Setup Options

### Option 1: Full Automated Setup

For production use with complete WordNet database:

```bash
yarn setup-wordnet:full
```

**Warning**: This loads ~155,000 words and takes 2-4 hours to complete.

### Option 2: Step-by-Step Setup

For more control over the process:

```bash
# Step 1: Download WordNet files
yarn download-wordnet

# Step 2: Load specific parts of speech
yarn load-wordnet-database:noun     # ~118k words, ~2 hours
yarn load-wordnet-database:verb     # ~11k words, ~15 minutes  
yarn load-wordnet-database:adj      # ~21k words, ~30 minutes
yarn load-wordnet-database:adv      # ~4k words, ~5 minutes

# Step 3: Check results
yarn setup-wordnet:stats
```

### Option 3: Custom Setup

Load specific amounts for testing or development:

```bash
# Load 5000 nouns
yarn load-wordnet-database --pos=noun --max-words=5000

# Load 1000 verbs  
yarn load-wordnet-database --pos=verb --max-words=1000

# Test run without saving to database
yarn load-wordnet-database --pos=noun --max-words=100 --dry-run
```

## Available Scripts

### Installation & Setup
- `yarn install-wordnet-deps` - Install required dependencies
- `yarn setup-wordnet` - Complete setup process
- `yarn setup-wordnet:quick` - Quick setup (1000 words)
- `yarn setup-wordnet:full` - Full setup (~155k words)
- `yarn setup-wordnet:stats` - Show current statistics

### Download & Extract
- `yarn download-wordnet` - Download WordNet files
- `yarn download-wordnet --force` - Force re-download

### Database Loading
- `yarn load-wordnet-database` - Load all WordNet data
- `yarn load-wordnet-database:stats` - Show statistics
- `yarn load-wordnet-database:noun` - Load nouns only
- `yarn load-wordnet-database:verb` - Load verbs only
- `yarn load-wordnet-database:adj` - Load adjectives only
- `yarn load-wordnet-database:adv` - Load adverbs only

### Testing & Development
- `yarn load-wordnet-database:test` - Test with 100 nouns (dry run)
- `yarn load-wordnet-database --dry-run` - Test without saving

## Script Options

### Download Options
```bash
yarn download-wordnet [options]

--force       Force re-download even if files exist
--no-verify   Skip file verification
--no-cleanup  Keep downloaded archives
--help        Show help message
```

### Database Loading Options
```bash
yarn load-wordnet-database [options]

--pos=<pos>           Part of speech (noun, verb, adj, adv)
--max-words=<count>   Maximum words to process
--batch-size=<size>   Batch size for processing (default: 100)
--dry-run            Run without saving to database
--force              Force reload even if data exists
--stats              Show statistics only
--help               Show help message
```

### Setup Options
```bash
yarn setup-wordnet [command] [options]

Commands:
  quick-start   Quick setup with 1000 common nouns
  full-setup    Complete setup with all WordNet data

Options:
  --force-download     Force re-download
  --skip-download      Skip download step
  --pos=<pos>         Load specific part of speech
  --max-words=<count> Maximum words to process
  --dry-run           Test without saving
  --stats             Show statistics only
```

## File Structure

After setup, WordNet files are organized as follows:

```
data/
└── wordnet/
    ├── downloads/          # Temporary download files (auto-cleaned)
    └── extracted/          # Extracted WordNet database files
        ├── data.noun       # Noun synset data
        ├── data.verb       # Verb synset data
        ├── data.adj        # Adjective synset data
        ├── data.adv        # Adverb synset data
        ├── index.noun      # Noun index
        ├── index.verb      # Verb index
        ├── index.adj       # Adjective index
        ├── index.adv       # Adverb index
        └── *.exc           # Exception lists
```

## Database Schema

WordNet data is stored in the following tables:

### Word Table
- `id` - Unique identifier
- `term` - The word/phrase
- `language` - Language (EN for WordNet)
- `wordnet_data` - Relationship to WordNet data

### WordNetData Table
- `word_id` - Foreign key to Word
- `synsets` - Array of synset definitions
- `lemma` - Base form of the word
- `hypernyms` - Broader terms (parent concepts)
- `hyponyms` - Narrower terms (child concepts)
- `holonyms` - Wholes that contain this part
- `meronyms` - Parts that make up this whole

## Performance Considerations

### Loading Times
- **Quick setup**: 5-10 minutes (1000 words)
- **Nouns only**: ~2 hours (118k words)
- **Complete database**: 2-4 hours (155k words)

### Database Size
- **Quick setup**: ~50 MB
- **Complete database**: ~500 MB - 1 GB

### Optimization Tips
1. **Use batch processing**: Larger batch sizes (200-500) for better performance
2. **Load by parts**: Load one part of speech at a time
3. **Monitor progress**: Use `--stats` to track progress
4. **Database tuning**: Ensure proper indexing and connection pooling

## Troubleshooting

### Common Issues

#### 1. Download Failures
```bash
# Check internet connection
curl -I http://wordnetcode.princeton.edu/3.0/WordNet-3.0.tar.gz

# Try alternative source
yarn download-wordnet --force
```

#### 2. Database Connection Issues
```bash
# Check database is running
yarn p:m

# Verify connection
psql $DATABASE_URL -c "SELECT 1;"
```

#### 3. Memory Issues
```bash
# Use smaller batch sizes
yarn load-wordnet-database --batch-size=50

# Load one part of speech at a time
yarn load-wordnet-database:noun
```

#### 4. Permission Issues
```bash
# Check directory permissions
ls -la data/

# Recreate directories
rm -rf data/wordnet
yarn download-wordnet
```

### Error Recovery

If loading fails partway through:

```bash
# Check what was loaded
yarn setup-wordnet:stats

# Continue from where it left off (scripts are idempotent)
yarn load-wordnet-database --pos=noun --force

# Or start fresh
yarn load-wordnet-database --force
```

## Integration with Application

Once WordNet data is loaded, it's automatically available in your application:

### API Usage
```typescript
// Search words with WordNet data
const words = await wordService.searchWordsWithWordNet('house');

// Get WordNet information
const wordNetInfo = await wordNetService.getWordNetInfo('house', Language.EN);
```

### UI Components
```tsx
// Display WordNet information
<WordNetInfo 
  wordNetData={word.wordnet_data} 
  term={word.term}
/>
```

## Maintenance

### Regular Updates
WordNet 3.0 is stable and doesn't require frequent updates. However, you may want to:

1. **Refresh data** if database becomes corrupted:
   ```bash
   yarn load-wordnet-database --force
   ```

2. **Add more words** as needed:
   ```bash
   yarn load-wordnet-database --pos=verb --max-words=5000
   ```

3. **Monitor database size** and performance:
   ```bash
   yarn setup-wordnet:stats
   ```

### Backup Recommendations
- **Backup WordNet data** before major updates
- **Export word lists** for migration purposes
- **Document custom modifications** to WordNet data

## Support

For issues or questions:
1. Check this guide and troubleshooting section
2. Review the console output for specific error messages
3. Use `--dry-run` to test without making changes
4. Check database logs for connection issues
5. Verify all dependencies are installed correctly

## References

- [WordNet Official Documentation](https://wordnet.princeton.edu/documentation)
- [WordNet Database Format](https://wordnet.princeton.edu/documentation/wndb5wn)
- [Princeton WordNet](https://wordnet.princeton.edu/)
- [Natural Language Processing Library](https://github.com/NaturalNode/natural)
