const baseConfig = require('./jest.config.js');

module.exports = {
	...baseConfig,
	displayName: 'Backend Tests',
	testEnvironment: '<rootDir>/src/test/jest-environment.js',
	testMatch: [
		'<rootDir>/src/backend/**/__tests__/**/*.{js,ts}',
		'<rootDir>/src/backend/**/*.{test,spec}.{js,ts}',
		'<rootDir>/src/app/api/**/__tests__/**/*.{js,ts}',
		'<rootDir>/src/app/api/**/*.{test,spec}.{js,ts}',
	],
	setupFilesAfterEnv: ['<rootDir>/src/test/backend-setup.ts'],
	collectCoverageFrom: [
		'src/backend/**/*.{js,ts}',
		'src/app/api/**/*.{js,ts}',
		'!src/**/*.d.ts',
		'!src/**/*.test.{js,ts}',
		'!src/**/__tests__/**',
		'!src/test/**',
	],
	// Use Next.js transform for backend tests
	transform: {
		'^.+\\.(js|ts)$': ['babel-jest', { presets: ['next/babel'] }],
	},
	// Add module name mapping for path aliases
	moduleNameMapper: {
		'^@/(.*)$': '<rootDir>/src/$1',
	},
};
