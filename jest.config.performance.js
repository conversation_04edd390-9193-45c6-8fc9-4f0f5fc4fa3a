const baseConfig = require('./jest.config.js');

module.exports = {
	...baseConfig,
	displayName: 'Performance Tests',
	testMatch: [
		'<rootDir>/src/**/__tests__/performance/**/*.test.{js,ts,tsx}',
		'<rootDir>/src/**/*.performance.test.{js,ts,tsx}',
	],
	testTimeout: 60000, // 60 seconds for performance tests
	maxWorkers: 1, // Run performance tests sequentially
	setupFilesAfterEnv: [
		'<rootDir>/src/test/setup.ts',
		'<rootDir>/src/test/performance-setup.ts',
	],
	collectCoverageFrom: [
		'src/backend/services/**/*.{js,ts}',
		'src/app/api/**/*.{js,ts}',
		'!src/**/*.test.{js,ts,tsx}',
		'!src/**/__tests__/**',
		'!src/test/**',
	],
	coverageThreshold: {
		global: {
			branches: 50,
			functions: 50,
			lines: 50,
			statements: 50,
		},
	},
	reporters: [
		'default',
		[
			'jest-junit',
			{
				outputDirectory: 'test-results/performance',
				outputName: 'performance-results.xml',
			},
		],
	],
};
